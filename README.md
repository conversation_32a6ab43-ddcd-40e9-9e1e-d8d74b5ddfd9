# Smart Factory WMS

A comprehensive Warehouse Management System designed for manufacturing environments with full traceability, production planning, and inventory control capabilities. Built with containerized deployment for per-customer deployment model.

## 🏭 Overview

Smart Factory WMS provides end-to-end management of manufacturing operations, from receiving raw materials to shipping finished goods. The system integrates inventory management, production planning, quality control, and logistics into a unified platform. Built with containerized architecture for per-customer deployment.

## ✨ Key Features

- **🐳 Containerized Architecture** for per-customer deployment
- **📦 Receiving Management** with lot tracking and quality inspection
- **🏭 Production Planning & MRP** with capacity planning
- **⚙️ Manufacturing Execution** tracking with OEE calculations
- **🔍 Quality Control** with defect tracking and traceability
- **📊 Inventory Management** with FIFO and expiration date control
- **🚚 Shipment Management** with document generation
- **📱 Mobile Support** for warehouse operations
- **🔐 JWT Authentication** with role-based access control
- **🌐 Multi-language Support** (EN, JA, ZH, VI)

## 🛠 Technology Stack

### Frontend Web
- **Framework**: Vue.js 3.x with Composition API
- **Language**: JavaScript (no TypeScript)
- **State Management**: Pinia
- **UI Framework**: Quasar Framework
- **CSS Framework**: TailwindCSS
- **Build Tool**: Vite
- **Testing**: Vitest + Cypress

### Frontend Mobile
- **Framework**: Flutter
- **State Management**: Provider pattern
- **Features**: Barcode scanning, offline capabilities

### Backend
- **Framework**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL 15+ with SQLAlchemy 2.0+
- **Cache**: Redis 7.0+
- **Authentication**: JWT with refresh tokens
- **Testing**: pytest with async support

### Infrastructure
- **Containerization**: Docker
- **Orchestration**: AWS ECS Fargate
- **CI/CD**: GitHub Actions
- **Monitoring**: CloudWatch
- **Storage**: AWS S3

## 📁 Project Structure

```
smart-factory-wms/
├── backend/                  # FastAPI backend application
│   ├── app/                  # Main application package
│   │   ├── api/v1/           # API endpoints (auth, users, inventory, etc.)
│   │   ├── core/             # Core functionality (config, database, security)
│   │   ├── models/           # SQLAlchemy database models
│   │   ├── schemas/          # Pydantic schemas
│   │   ├── services/         # Business logic services
│   │   ├── middleware/       # Custom middleware
│   │   └── utils/            # Utility functions
│   ├── tests/                # Backend tests
│   ├── requirements/         # Python dependencies (base, dev, prod)
│   ├── migrations/           # Database migrations
│   └── docker/               # Docker configurations
├── frontend-web/             # Vue.js frontend application (Quasar Framework)
│   ├── src/                  # Source files
│   │   ├── components/       # Vue components (common, auth, inventory, etc.)
│   │   ├── pages/            # Vue pages/views
│   │   ├── layouts/          # Quasar layouts
│   │   ├── stores/           # Pinia store modules
│   │   ├── router/           # Vue Router configuration
│   │   ├── boot/             # Quasar boot files
│   │   ├── services/         # API services
│   │   ├── composables/      # Vue 3 composables
│   │   ├── utils/            # Utility functions
│   │   ├── css/              # Styles (TailwindCSS + Quasar)
│   │   ├── assets/           # Static assets
│   │   └── i18n/             # Internationalization files
│   ├── tests/                # Frontend tests
│   └── docker/               # Docker configurations
├── frontend-mobile/          # Flutter mobile application
│   ├── lib/                  # Dart source code
│   │   ├── core/             # Core functionality
│   │   ├── models/           # Data models
│   │   ├── services/         # API and business services
│   │   ├── screens/          # UI screens
│   │   ├── widgets/          # Reusable widgets
│   │   └── utils/            # Utility functions
│   ├── assets/               # Mobile app assets
│   └── test/                 # Mobile app tests
├── infrastructure/           # Infrastructure as code
│   ├── docker-compose/       # Complete application stack
│   ├── terraform/            # AWS infrastructure
│   └── scripts/              # Deployment scripts
├── scripts/                  # Project scripts
│   ├── setup/                # Setup and initialization
│   ├── deployment/           # Deployment automation
│   └── maintenance/          # Maintenance scripts
├── tests/                    # Integration and performance tests
├── docs/                     # Comprehensive documentation
│   ├── en/                   # English documentation
│   ├── ja/                   # Japanese documentation
│   └── detail_design/        # Detailed design documents
├── .github/                  # GitHub Actions workflows
└── project.json              # Project configuration
```

## 🚀 Quick Start

### Prerequisites

- **Docker** and **Docker Compose**
- **Node.js** 18+ (for frontend development)
- **Python** 3.11+ (for backend development)
- **Flutter** 3.16+ (for mobile development, optional)

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd smart-factory-wms

# Run the setup script
./scripts/setup/setup.sh
```

### 2. Environment Configuration

Copy and configure environment files:

```bash
# Backend environment
cp backend/.env.example backend/.env

# Frontend environment
cp frontend-web/.env.example frontend-web/.env

# Infrastructure environment
cp infrastructure/docker-compose/.env.example infrastructure/docker-compose/.env
```

Update the environment variables according to your setup.

### 3. Start Development Environment

```bash
# Start the complete stack
cd infrastructure/docker-compose
docker-compose up -d

# Or use the deployment script
./scripts/deployment/deploy.sh -e development
```

### 4. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database Admin**: http://localhost:5050 (PgAdmin)
- **Redis Admin**: http://localhost:8081 (Redis Commander)

## 🧪 Development

### Backend Development

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements/dev.txt

# Run development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development

```bash
cd frontend-web

# Install dependencies
npm install

# Run development server
npm run dev
```

### Mobile Development

```bash
cd frontend-mobile

# Get dependencies
flutter pub get

# Run on emulator/device
flutter run
```

## 🧪 Testing

### Backend Tests

```bash
cd backend
pytest tests/ -v --cov=app --cov-report=html
```

### Frontend Tests

```bash
cd frontend-web

# Unit tests
npm run test:unit

# E2E tests
npm run test:e2e
```

### Mobile Tests

```bash
cd frontend-mobile
flutter test
```

## 📚 Documentation

### 🇺🇸 English Documentation

- [Project Document](docs/en/01_project_document.md) - Overview and objectives
- [Development Plan](docs/en/02_development_plan.md) - Development approach and phases
- [Architecture Document](docs/en/03_architecture_document.md) - System architecture and components
- [Database Design](docs/en/04_database_design.md) - Database schema and relationships
- [AI Development Issues Log](docs/en/05_ai_development_issues.md) - AI-assisted development challenges and solutions
- [Coding Standards & Guidelines](docs/en/06_coding_standards.md) - Coding rules and best practices
- [AI Prompt Engineering Guide](docs/en/07_ai_prompt_template.md) - Comprehensive AI-assisted development guide
- [Feature Overview](docs/en/08_feature_overview.md) - System features and user stories

### 🇯🇵 日本語ドキュメント (Japanese Documentation)

- [プロジェクト文書](docs/ja/01_project_document.md) - プロジェクト概要と目的
- [開発計画](docs/ja/02_development_plan.md) - 開発アプローチとフェーズ
- [アーキテクチャ文書](docs/ja/03_architecture_document.md) - システムアーキテクチャとコンポーネント
- [データベース設計](docs/ja/04_database_design.md) - データベーススキーマと関係
- [AI開発課題ログ](docs/ja/05_ai_development_issues.md) - AI支援開発の課題と解決策
- [コーディング標準・ガイドライン](docs/ja/06_coding_standards.md) - コーディングルールとベストプラクティス
- [AIプロンプトエンジニアリングガイド](docs/ja/07_ai_prompt_template.md) - 包括的なAI支援開発ガイド
- [機能概要](docs/ja/08_feature_overview.md) - システム機能とユーザーストーリー

### 📚 Documentation Index
For a complete overview of available documentation in all languages, see [docs/README.md](docs/README.md).

## 🚀 Deployment

### Development Deployment

```bash
./scripts/deployment/deploy.sh -e development
```

### Staging Deployment

```bash
./scripts/deployment/deploy.sh -e staging
```

### Production Deployment

```bash
./scripts/deployment/deploy.sh -e production
```

## 🏗 Architecture

The Smart Factory WMS follows a microservices architecture with:

- **Per-customer deployment model** for data isolation
- **Containerized services** for scalability and portability
- **Event-driven architecture** for real-time updates
- **RESTful APIs** with OpenAPI documentation
- **JWT-based authentication** with RBAC authorization
- **Comprehensive audit logging** for compliance

## 🔧 Configuration

Key configuration files:

- `project.json` - Project-wide configuration
- `backend/.env` - Backend environment variables
- `frontend-web/.env` - Frontend environment variables
- `infrastructure/docker-compose/.env` - Infrastructure configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:

- Check the [documentation](docs/README.md)
- Review [coding standards](docs/en/06_coding_standards.md)
- Consult the [AI development guide](docs/en/07_ai_prompt_template.md)

---

**Smart Factory WMS** - Comprehensive manufacturing operations management with containerized deployment.

