name: smart_factory_wms
description: Smart Factory WMS Mobile Application - Flutter app for warehouse operations

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_localizations:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication & Security
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  
  # Barcode & QR Code
  qr_code_scanner: ^1.0.1
  barcode_widget: ^2.0.3
  qr_flutter: ^4.1.0
  
  # Camera & Image
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  image: ^4.1.3
  
  # File Handling
  path_provider: ^2.1.1
  file_picker: ^6.1.1
  open_file: ^3.3.2
  
  # Networking & Connectivity
  connectivity_plus: ^5.0.2
  internet_connection_checker: ^1.0.0+1
  
  # Date & Time
  intl: ^0.18.1
  
  # Utilities
  uuid: ^4.2.1
  logger: ^2.0.2+1
  equatable: ^2.0.5
  
  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0
  
  # Charts & Graphs
  fl_chart: ^0.65.0
  
  # Permissions
  permission_handler: ^11.1.0
  
  # Device Info
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # Background Tasks
  workmanager: ^0.5.2
  
  # Push Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  
  # Linting & Analysis
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/config/

  # Fonts
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700

# Flutter configuration
flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/generated
  use_deferred_loading: false
