#!/bin/bash

# Smart Factory WMS - Setup Script
# This script sets up the development environment

set -e

echo "🏭 Smart Factory WMS - Development Setup"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Check if Python is installed
check_python() {
    print_status "Checking Python installation..."
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.11+ first."
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 11) else 1)"; then
        print_error "Python 3.11 or higher is required. Current version: $(python3 --version)"
        exit 1
    fi
    
    print_success "Python $(python3 --version) is installed"
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env 2>/dev/null || echo "# Backend environment variables" > backend/.env
        print_success "Created backend/.env"
    else
        print_warning "backend/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend-web/.env" ]; then
        cp frontend-web/.env.example frontend-web/.env 2>/dev/null || echo "# Frontend environment variables" > frontend-web/.env
        print_success "Created frontend-web/.env"
    else
        print_warning "frontend-web/.env already exists"
    fi
    
    # Infrastructure environment
    if [ ! -f "infrastructure/docker-compose/.env" ]; then
        cp infrastructure/docker-compose/.env.example infrastructure/docker-compose/.env
        print_success "Created infrastructure/docker-compose/.env"
    else
        print_warning "infrastructure/docker-compose/.env already exists"
    fi
}

# Install backend dependencies
setup_backend() {
    print_status "Setting up backend dependencies..."
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Created Python virtual environment"
    fi
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements/dev.txt
    
    print_success "Backend dependencies installed"
    cd ..
}

# Install frontend dependencies
setup_frontend() {
    print_status "Setting up frontend dependencies..."
    
    cd frontend-web
    
    # Install npm dependencies
    npm install
    
    print_success "Frontend dependencies installed"
    cd ..
}

# Setup Flutter mobile app
setup_mobile() {
    print_status "Setting up mobile app dependencies..."
    
    if command -v flutter &> /dev/null; then
        cd frontend-mobile
        flutter pub get
        print_success "Mobile app dependencies installed"
        cd ..
    else
        print_warning "Flutter not installed. Skipping mobile app setup."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    mkdir -p ssl
    
    print_success "Directories created"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    cd infrastructure/docker-compose
    
    # Start only database services
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations (when implemented)
    # docker-compose exec backend alembic upgrade head
    
    print_success "Database setup completed"
    cd ../..
}

# Main setup function
main() {
    echo
    print_status "Starting Smart Factory WMS setup..."
    echo
    
    # Check prerequisites
    check_docker
    check_nodejs
    check_python
    
    echo
    print_status "Prerequisites check passed!"
    echo
    
    # Setup project
    setup_env_files
    create_directories
    setup_backend
    setup_frontend
    setup_mobile
    setup_database
    
    echo
    print_success "🎉 Smart Factory WMS setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "  1. Review and update environment files:"
    echo "     - backend/.env"
    echo "     - frontend-web/.env"
    echo "     - infrastructure/docker-compose/.env"
    echo
    echo "  2. Start the development environment:"
    echo "     cd infrastructure/docker-compose"
    echo "     docker-compose up -d"
    echo
    echo "  3. Access the application:"
    echo "     - Frontend: http://localhost:3000"
    echo "     - Backend API: http://localhost:8000"
    echo "     - API Documentation: http://localhost:8000/docs"
    echo "     - PgAdmin: http://localhost:5050"
    echo
    print_status "Happy coding! 🚀"
}

# Run main function
main "$@"
