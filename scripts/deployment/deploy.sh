#!/bin/bash

# Smart Factory WMS - Deployment Script
# This script handles deployment to different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="staging"
BUILD_IMAGES=true
RUN_MIGRATIONS=true
BACKUP_DB=true

# Usage function
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Target environment (development|staging|production)"
    echo "  -n, --no-build          Skip building Docker images"
    echo "  -s, --skip-migrations   Skip running database migrations"
    echo "  -b, --no-backup         Skip database backup"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e production"
    echo "  $0 --environment staging --no-build"
    echo "  $0 -e development -n -s"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--no-build)
            BUILD_IMAGES=false
            shift
            ;;
        -s|--skip-migrations)
            RUN_MIGRATIONS=false
            shift
            ;;
        -b|--no-backup)
            BACKUP_DB=false
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: development, staging, production"
    exit 1
fi

echo "🏭 Smart Factory WMS - Deployment"
echo "================================="
echo "Environment: $ENVIRONMENT"
echo "Build Images: $BUILD_IMAGES"
echo "Run Migrations: $RUN_MIGRATIONS"
echo "Backup Database: $BACKUP_DB"
echo

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Load environment configuration
load_environment() {
    print_status "Loading environment configuration..."
    
    ENV_FILE="infrastructure/docker-compose/.env"
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file not found: $ENV_FILE"
        print_error "Please run setup script first or create the environment file"
        exit 1
    fi
    
    source "$ENV_FILE"
    print_success "Environment configuration loaded"
}

# Backup database
backup_database() {
    if [ "$BACKUP_DB" = true ] && [ "$ENVIRONMENT" != "development" ]; then
        print_status "Creating database backup..."
        
        BACKUP_DIR="backups"
        mkdir -p "$BACKUP_DIR"
        
        BACKUP_FILE="$BACKUP_DIR/smart_factory_$(date +%Y%m%d_%H%M%S).sql"
        
        cd infrastructure/docker-compose
        docker-compose exec -T postgres pg_dump -U "$POSTGRES_USER" "$POSTGRES_DB" > "../../$BACKUP_FILE"
        cd ../..
        
        print_success "Database backup created: $BACKUP_FILE"
    else
        print_warning "Skipping database backup"
    fi
}

# Build Docker images
build_images() {
    if [ "$BUILD_IMAGES" = true ]; then
        print_status "Building Docker images..."
        
        cd infrastructure/docker-compose
        
        # Build backend image
        print_status "Building backend image..."
        docker-compose build backend
        
        # Build frontend image
        print_status "Building frontend image..."
        docker-compose build frontend-web
        
        cd ../..
        print_success "Docker images built successfully"
    else
        print_warning "Skipping Docker image build"
    fi
}

# Run database migrations
run_migrations() {
    if [ "$RUN_MIGRATIONS" = true ]; then
        print_status "Running database migrations..."
        
        cd infrastructure/docker-compose
        
        # Ensure database is running
        docker-compose up -d postgres
        
        # Wait for database to be ready
        sleep 5
        
        # Run migrations (when Alembic is set up)
        # docker-compose run --rm backend alembic upgrade head
        
        cd ../..
        print_success "Database migrations completed"
    else
        print_warning "Skipping database migrations"
    fi
}

# Deploy application
deploy_application() {
    print_status "Deploying application..."
    
    cd infrastructure/docker-compose
    
    # Set environment-specific compose file
    COMPOSE_FILE="docker-compose.yml"
    if [ "$ENVIRONMENT" = "development" ]; then
        COMPOSE_FILE="docker-compose.yml:docker-compose.dev.yml"
    fi
    
    # Deploy services
    COMPOSE_FILE="$COMPOSE_FILE" docker-compose up -d
    
    cd ../..
    print_success "Application deployed successfully"
}

# Health check
health_check() {
    print_status "Performing health check..."
    
    # Wait for services to start
    sleep 10
    
    # Check backend health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Backend is healthy"
    else
        print_error "Backend health check failed"
        return 1
    fi
    
    # Check frontend health (if not development)
    if [ "$ENVIRONMENT" != "development" ]; then
        if curl -f http://localhost:3000/health > /dev/null 2>&1; then
            print_success "Frontend is healthy"
        else
            print_error "Frontend health check failed"
            return 1
        fi
    fi
    
    print_success "All services are healthy"
}

# Show deployment info
show_deployment_info() {
    echo
    print_success "🎉 Deployment completed successfully!"
    echo
    print_status "Application URLs:"
    
    if [ "$ENVIRONMENT" = "development" ]; then
        echo "  - Frontend (Dev): http://localhost:3000"
        echo "  - Backend API: http://localhost:8000"
        echo "  - API Documentation: http://localhost:8000/docs"
        echo "  - PgAdmin: http://localhost:5050"
        echo "  - Redis Commander: http://localhost:8081"
    else
        echo "  - Application: http://localhost"
        echo "  - API: http://localhost/api"
        echo "  - API Documentation: http://localhost/api/docs"
    fi
    
    echo
    print_status "Useful commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart services: docker-compose restart"
    echo "  - Update services: docker-compose pull && docker-compose up -d"
    echo
}

# Main deployment function
main() {
    print_status "Starting deployment to $ENVIRONMENT environment..."
    echo
    
    check_prerequisites
    load_environment
    backup_database
    build_images
    run_migrations
    deploy_application
    health_check
    show_deployment_info
}

# Run main function
main "$@"
