{"name": "smart-factory-wms", "version": "1.0.0", "description": "Smart Factory Warehouse Management System with containerized deployment", "type": "monorepo", "architecture": "microservices", "deployment_model": "per-customer-containerized", "project_info": {"created": "2025-07-15", "license": "Proprietary", "maintainers": ["Smart Factory Development Team"], "repository": "https://github.com/organization/smart-factory-wms", "documentation": "docs/README.md"}, "technology_stack": {"frontend_web": {"framework": "Vue.js 3.x", "state_management": "Vuex", "routing": "Vue Router", "ui_library": "Vuetify 3", "build_tool": "Vite", "language": "JavaScript/TypeScript", "testing": "Vitest + Cypress"}, "frontend_mobile": {"framework": "Flutter", "language": "Dart", "state_management": "Provider", "features": ["barcode_scanning", "offline_capabilities"], "testing": "Flutter Test"}, "backend": {"framework": "FastAPI", "language": "Python 3.11+", "async_support": "asyncio/asyncpg", "authentication": "JWT", "validation": "Pydantic", "orm": "SQLAlchemy 2.0+", "testing": "pytest"}, "database": {"primary": "PostgreSQL 15+", "caching": "Redis 7.0+", "connection_pooling": "PgBouncer"}, "infrastructure": {"containerization": "<PERSON>er", "orchestration": "AWS ECS Fargate", "api_gateway": "AWS API Gateway", "monitoring": "CloudWatch", "secrets": "AWS Secrets Manager", "storage": "AWS S3"}}, "modules": [{"name": "user_management", "description": "User authentication and authorization", "priority": "phase_1", "status": "planned"}, {"name": "receiving_management", "description": "Receiving orders and quality inspection", "priority": "phase_2", "status": "planned"}, {"name": "inventory_management", "description": "Real-time inventory tracking and control", "priority": "phase_2", "status": "planned"}, {"name": "shipment_management", "description": "Shipment planning and execution", "priority": "phase_2", "status": "planned"}, {"name": "production_management", "description": "Production planning and execution", "priority": "phase_3", "status": "planned"}, {"name": "reporting_analytics", "description": "KPI dashboards and custom reports", "priority": "phase_4", "status": "planned"}], "environments": {"development": {"database_url": "postgresql://localhost:5432/smart_factory_dev", "redis_url": "redis://localhost:6379/0", "debug": true, "log_level": "DEBUG"}, "staging": {"database_url": "${DATABASE_URL}", "redis_url": "${REDIS_URL}", "debug": false, "log_level": "INFO"}, "production": {"database_url": "${DATABASE_URL}", "redis_url": "${REDIS_URL}", "debug": false, "log_level": "WARNING"}}, "coding_standards": {"backend": {"naming": {"files": "snake_case", "classes": "PascalCase", "functions": "snake_case", "variables": "snake_case", "constants": "UPPER_SNAKE_CASE"}, "documentation": "required_docstrings", "testing": "minimum_80_percent_coverage"}, "frontend": {"naming": {"components": "PascalCase", "props": "camelCase", "events": "kebab-case", "css_classes": "kebab-case"}, "state_management": "vuex_modules_by_domain", "testing": "unit_and_e2e_tests"}}, "security": {"authentication": {"method": "JWT", "token_expiry": {"access": "30_minutes", "refresh": "7_days"}}, "authorization": "RBAC", "encryption": {"at_rest": true, "in_transit": "TLS_1_3"}}, "deployment": {"strategy": "containerized_per_customer", "base_images": {"backend": "python:3.11-slim", "frontend": "node:18-alpine", "database": "postgres:15-alpine", "cache": "redis:7-alpine"}, "health_checks": true, "auto_scaling": true}, "development": {"branching_strategy": {"main": "production_releases", "develop": "development_integration", "feature": "feature/feature-name", "bugfix": "bugfix/issue-description"}, "code_review": "required", "ci_cd": "github_actions"}, "performance_targets": {"api_response_time": "< 200ms", "page_load_time": "< 3s", "concurrent_users": "1000+", "database_query_time": "< 100ms"}, "compliance": {"data_privacy": "GDPR", "security": "OWASP_Top_10", "accessibility": "WCAG_2_1_AA", "internationalization": ["en", "ja", "zh", "vi"]}}