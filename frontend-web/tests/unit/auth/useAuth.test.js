/**
 * Unit tests for useAuth composable
 */

import { beforeEach, describe, expect, it, vi } from "vitest";
import { createApp } from "vue";
import { createI18n } from "vue-i18n";
import { createRouter, createWebHistory } from "vue-router";
import { createStore } from "vuex";

// Mock the composable since we can't import it directly in tests
const mockUseAuth = () => {
  const loginForm = {
    username: "",
    password: "",
    rememberMe: false,
  };

  const showPassword = { value: false };
  const isLoading = { value: false };
  const errors = {
    username: "",
    password: "",
    general: "",
  };

  const canAttemptLogin = { value: true };
  const getRemainingLockoutTime = { value: 0 };

  return {
    loginForm,
    showPassword,
    isLoading,
    errors,
    canAttemptLogin,
    getRemainingLockoutTime,
    validateField: vi.fn(),
    validateForm: vi.fn(),
    login: vi.fn(),
    resetForm: vi.fn(),
    clearErrors: vi.fn(),
    togglePasswordVisibility: vi.fn(),
  };
};

// Test setup
const createTestApp = () => {
  const store = createStore({
    modules: {
      auth: {
        namespaced: true,
        state: {
          isLoading: false,
          user: null,
          loginAttempts: 0,
          lastLoginAttempt: null,
          redirectRoute: null,
        },
        getters: {
          isAuthenticated: () => false,
        },
        mutations: {
          CLEAR_REDIRECT_ROUTE: vi.fn(),
        },
        actions: {
          login: vi.fn(),
          updateLoginForm: vi.fn(),
        },
      },
    },
  });

  const router = createRouter({
    history: createWebHistory(),
    routes: [
      { path: "/login", name: "Login" },
      { path: "/dashboard", name: "Dashboard" },
    ],
  });

  const i18n = createI18n({
    legacy: false,
    locale: "en",
    messages: {
      en: {
        auth: {
          validation: {
            username_required: "Username is required",
            username_min_length: "Username must be at least 3 characters",
            username_max_length: "Username must not exceed 50 characters",
            username_invalid: "Username contains invalid characters",
            password_required: "Password is required",
            password_min_length: "Password must be at least 8 characters",
            password_max_length: "Password must not exceed 128 characters",
          },
          errors: {
            invalid_credentials: "Invalid credentials",
            account_locked: "Account locked",
            too_many_requests: "Too many requests",
            network_error: "Network error",
            server_error: "Server error",
          },
        },
      },
    },
  });

  const app = createApp({});
  app.use(store);
  app.use(router);
  app.use(i18n);

  return { app, store, router, i18n };
};

describe("useAuth", () => {
  let testApp, store, router;

  beforeEach(() => {
    const setup = createTestApp();
    testApp = setup.app;
    store = setup.store;
    router = setup.router;
    vi.clearAllMocks();
  });

  describe("Form Validation", () => {
    it("should validate username field correctly", () => {
      const { validateField } = useAuth();

      // Test required validation
      expect(validateField("username", "")).toBe("Username is required");
      expect(validateField("username", "   ")).toBe("Username is required");

      // Test minimum length validation
      expect(validateField("username", "ab")).toBe(
        "Username must be at least 3 characters"
      );

      // Test maximum length validation
      const longUsername = "a".repeat(51);
      expect(validateField("username", longUsername)).toBe(
        "Username must not exceed 50 characters"
      );

      // Test invalid characters
      expect(validateField("username", "user@#$")).toBe(
        "Username contains invalid characters"
      );

      // Test valid usernames
      expect(validateField("username", "user123")).toBe("");
      expect(validateField("username", "user.name")).toBe("");
      expect(validateField("username", "user_name")).toBe("");
      expect(validateField("username", "<EMAIL>")).toBe("");
    });

    it("should validate password field correctly", () => {
      const { validateField } = useAuth();

      // Test required validation
      expect(validateField("password", "")).toBe("Password is required");

      // Test minimum length validation
      expect(validateField("password", "1234567")).toBe(
        "Password must be at least 8 characters"
      );

      // Test maximum length validation
      const longPassword = "a".repeat(129);
      expect(validateField("password", longPassword)).toBe(
        "Password must not exceed 128 characters"
      );

      // Test valid password
      expect(validateField("password", "password123")).toBe("");
    });

    it("should validate entire form correctly", () => {
      const { loginForm, validateForm } = useAuth();

      // Test invalid form
      loginForm.username = "";
      loginForm.password = "";
      expect(validateForm()).toBe(false);

      // Test valid form
      loginForm.username = "testuser";
      loginForm.password = "password123";
      expect(validateForm()).toBe(true);
    });
  });

  describe("Authentication Actions", () => {
    it("should handle successful login", async () => {
      const mockLoginResult = {
        success: true,
        user: { id: 1, username: "testuser" },
      };

      store.dispatch = vi.fn().mockResolvedValue(mockLoginResult);
      router.push = vi.fn();

      const { loginForm, login } = useAuth();

      loginForm.username = "testuser";
      loginForm.password = "password123";
      loginForm.rememberMe = true;

      const result = await login();

      expect(store.dispatch).toHaveBeenCalledWith("auth/login", {
        username: "testuser",
        password: "password123",
        rememberMe: true,
      });

      expect(result.success).toBe(true);
      expect(router.push).toHaveBeenCalled();
    });

    it("should handle login failure", async () => {
      const mockError = new Error("Invalid credentials");
      mockError.code = 401;

      store.dispatch = vi.fn().mockRejectedValue(mockError);

      const { loginForm, login, errors } = useAuth();

      loginForm.username = "testuser";
      loginForm.password = "wrongpassword";

      const result = await login();

      expect(result.success).toBe(false);
      expect(errors.general).toBe("Invalid credentials");
    });

    it("should handle network errors", async () => {
      const mockError = new Error("Network error");
      store.dispatch = vi.fn().mockRejectedValue(mockError);

      const { loginForm, login, errors } = useAuth();

      loginForm.username = "testuser";
      loginForm.password = "password123";

      const result = await login();

      expect(result.success).toBe(false);
      expect(errors.general).toBe("Network error");
    });

    it("should prevent login when validation fails", async () => {
      const { loginForm, login } = useAuth();

      loginForm.username = ""; // Invalid
      loginForm.password = "password123";

      const result = await login();

      expect(store.dispatch).not.toHaveBeenCalled();
      expect(result.success).toBe(false);
    });
  });

  describe("Rate Limiting", () => {
    it("should respect rate limiting", () => {
      // Mock rate limiting state
      store.state.auth.loginAttempts = 5;
      store.state.auth.lastLoginAttempt = Date.now();

      const { canAttemptLogin } = useAuth();

      expect(canAttemptLogin.value).toBe(false);
    });

    it("should allow login after lockout period", () => {
      // Mock expired lockout
      store.state.auth.loginAttempts = 5;
      store.state.auth.lastLoginAttempt = Date.now() - 16 * 60 * 1000; // 16 minutes ago

      const { canAttemptLogin } = useAuth();

      expect(canAttemptLogin.value).toBe(true);
    });
  });

  describe("Form Utilities", () => {
    it("should reset form correctly", () => {
      const { loginForm, resetForm, errors } = useAuth();

      // Set form data
      loginForm.username = "testuser";
      loginForm.password = "password123";
      loginForm.rememberMe = true;
      errors.general = "Some error";

      resetForm();

      expect(loginForm.username).toBe("");
      expect(loginForm.password).toBe("");
      expect(loginForm.rememberMe).toBe(false);
      expect(errors.general).toBe("");
    });

    it("should clear errors correctly", () => {
      const { errors, clearErrors } = useAuth();

      errors.username = "Username error";
      errors.password = "Password error";
      errors.general = "General error";

      clearErrors();

      expect(errors.username).toBe("");
      expect(errors.password).toBe("");
      expect(errors.general).toBe("");
    });

    it("should toggle password visibility", () => {
      const { showPassword, togglePasswordVisibility } = useAuth();

      expect(showPassword.value).toBe(false);

      togglePasswordVisibility();
      expect(showPassword.value).toBe(true);

      togglePasswordVisibility();
      expect(showPassword.value).toBe(false);
    });
  });

  describe("Error Mapping", () => {
    it("should map error codes to appropriate messages", async () => {
      const testCases = [
        { code: 404, expectedMessage: "Invalid credentials" },
        { code: 401, expectedMessage: "Invalid credentials" },
        { code: 423, expectedMessage: "Account locked" },
        { code: 429, expectedMessage: "Too many requests" },
        { code: 500, expectedMessage: "Server error" },
      ];

      for (const testCase of testCases) {
        const mockError = new Error("Test error");
        mockError.code = testCase.code;

        store.dispatch = vi.fn().mockRejectedValue(mockError);

        const { loginForm, login, errors } = useAuth();

        loginForm.username = "testuser";
        loginForm.password = "password123";

        await login();

        expect(errors.general).toBe(testCase.expectedMessage);
      }
    });
  });
});
