/**
 * Unit tests for auth store module
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import authModule from '@/store/modules/auth'

// Mock API
const mockAPI = {
  post: vi.fn(),
  get: vi.fn()
}

vi.mock('@/services/api', () => ({
  default: mockAPI
}))

vi.mock('jwt-decode', () => ({
  jwtDecode: vi.fn(() => ({
    exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
  }))
}))

describe('Auth Store Module', () => {
  let store

  beforeEach(() => {
    // Reset store state
    store = {
      state: { ...authModule.state },
      getters: {},
      commit: vi.fn(),
      dispatch: vi.fn()
    }

    // Setup getters
    Object.keys(authModule.getters).forEach(key => {
      store.getters[key] = authModule.getters[key](store.state, store.getters)
    })

    vi.clearAllMocks()
  })

  describe('State', () => {
    it('should have correct initial state', () => {
      expect(store.state.token).toBeNull()
      expect(store.state.refreshToken).toBeNull()
      expect(store.state.user).toBeNull()
      expect(store.state.isLoading).toBe(false)
      expect(store.state.loginAttempts).toBe(0)
      expect(store.state.lastLoginAttempt).toBeNull()
    })
  })

  describe('Getters', () => {
    it('should return correct authentication status', () => {
      // Not authenticated initially
      expect(store.getters.isAuthenticated).toBe(false)

      // Set token and user
      store.state.token = 'test-token'
      store.state.user = { id: 1, username: 'testuser' }
      
      // Recalculate getter
      store.getters.isAuthenticated = authModule.getters.isAuthenticated(store.state, store.getters)
      expect(store.getters.isAuthenticated).toBe(true)
    })

    it('should return user information', () => {
      const testUser = { id: 1, username: 'testuser', email: '<EMAIL>' }
      store.state.user = testUser

      store.getters.userId = authModule.getters.userId(store.state)
      store.getters.userName = authModule.getters.userName(store.state)
      store.getters.userEmail = authModule.getters.userEmail(store.state)

      expect(store.getters.userId).toBe(1)
      expect(store.getters.userName).toBe('testuser')
      expect(store.getters.userEmail).toBe('<EMAIL>')
    })

    it('should check user roles correctly', () => {
      store.state.user = {
        roles: [
          { name: 'admin', permissions: [{ name: 'users.create' }] },
          { name: 'manager', permissions: [{ name: 'inventory.view' }] }
        ]
      }

      const hasRole = authModule.getters.hasRole(store.state, store.getters)
      expect(hasRole('admin')).toBe(true)
      expect(hasRole('user')).toBe(false)
    })

    it('should check user permissions correctly', () => {
      store.state.user = {
        roles: [
          { name: 'admin', permissions: [{ name: 'users.create' }, { name: 'users.delete' }] }
        ]
      }

      store.getters.userPermissions = authModule.getters.userPermissions(store.state)
      const hasPermission = authModule.getters.hasPermission(store.state, store.getters)
      const hasPermissions = authModule.getters.hasPermissions(store.state, store.getters)

      expect(hasPermission('users.create')).toBe(true)
      expect(hasPermission('inventory.view')).toBe(false)
      expect(hasPermissions(['users.create', 'users.delete'])).toBe(true)
      expect(hasPermissions(['users.create', 'inventory.view'])).toBe(false)
    })
  })

  describe('Mutations', () => {
    it('should set loading state', () => {
      authModule.mutations.SET_LOADING(store.state, true)
      expect(store.state.isLoading).toBe(true)

      authModule.mutations.SET_LOADING(store.state, false)
      expect(store.state.isLoading).toBe(false)
    })

    it('should set token', () => {
      const testToken = 'test-token-123'
      authModule.mutations.SET_TOKEN(store.state, testToken)
      expect(store.state.token).toBe(testToken)
    })

    it('should set user', () => {
      const testUser = { id: 1, username: 'testuser' }
      authModule.mutations.SET_USER(store.state, testUser)
      expect(store.state.user).toEqual(testUser)
    })

    it('should increment login attempts', () => {
      const initialAttempts = store.state.loginAttempts
      authModule.mutations.INCREMENT_LOGIN_ATTEMPTS(store.state)
      expect(store.state.loginAttempts).toBe(initialAttempts + 1)
      expect(store.state.lastLoginAttempt).toBeTypeOf('number')
    })

    it('should reset login attempts', () => {
      store.state.loginAttempts = 5
      store.state.lastLoginAttempt = Date.now()

      authModule.mutations.RESET_LOGIN_ATTEMPTS(store.state)
      expect(store.state.loginAttempts).toBe(0)
      expect(store.state.lastLoginAttempt).toBeNull()
    })

    it('should clear auth data', () => {
      store.state.token = 'test-token'
      store.state.refreshToken = 'refresh-token'
      store.state.user = { id: 1 }

      authModule.mutations.CLEAR_AUTH(store.state)
      expect(store.state.token).toBeNull()
      expect(store.state.refreshToken).toBeNull()
      expect(store.state.user).toBeNull()
    })
  })

  describe('Actions', () => {
    let context

    beforeEach(() => {
      context = {
        commit: vi.fn(),
        dispatch: vi.fn(),
        state: { ...authModule.state },
        getters: {}
      }
    })

    it('should handle successful login', async () => {
      const mockResponse = {
        data: {
          access_token: 'access-token',
          refresh_token: 'refresh-token',
          user: { id: 1, username: 'testuser' }
        }
      }

      mockAPI.post.mockResolvedValue(mockResponse)

      const credentials = {
        username: 'testuser',
        password: 'password123',
        rememberMe: true
      }

      const result = await authModule.actions.login(context, credentials)

      expect(mockAPI.post).toHaveBeenCalledWith('/auth/v1/login', {
        username: 'testuser',
        password: 'password123',
        remember_me: true
      })

      expect(context.commit).toHaveBeenCalledWith('SET_TOKEN', 'access-token')
      expect(context.commit).toHaveBeenCalledWith('SET_REFRESH_TOKEN', 'refresh-token')
      expect(context.commit).toHaveBeenCalledWith('SET_USER', mockResponse.data.user)
      expect(context.commit).toHaveBeenCalledWith('RESET_LOGIN_ATTEMPTS')

      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockResponse.data.user)
    })

    it('should handle login failure', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { message: 'Invalid credentials' }
        }
      }

      mockAPI.post.mockRejectedValue(mockError)

      const credentials = {
        username: 'testuser',
        password: 'wrongpassword'
      }

      try {
        await authModule.actions.login(context, credentials)
      } catch (error) {
        expect(context.commit).toHaveBeenCalledWith('INCREMENT_LOGIN_ATTEMPTS')
        expect(error.message).toBe('Invalid credentials')
        expect(error.code).toBe(401)
      }
    })

    it('should handle logout', async () => {
      context.state.token = 'test-token'
      mockAPI.post.mockResolvedValue({})

      await authModule.actions.logout(context)

      expect(mockAPI.post).toHaveBeenCalledWith('/auth/logout')
      expect(context.commit).toHaveBeenCalledWith('CLEAR_AUTH')
      expect(context.dispatch).toHaveBeenCalledWith('clearTokenRefresh')
    })

    it('should handle token refresh', async () => {
      const mockResponse = {
        data: {
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token'
        }
      }

      context.state.refreshToken = 'old-refresh-token'
      mockAPI.post.mockResolvedValue(mockResponse)

      const result = await authModule.actions.refreshToken(context)

      expect(mockAPI.post).toHaveBeenCalledWith('/auth/refresh', {
        refresh_token: 'old-refresh-token'
      })

      expect(context.commit).toHaveBeenCalledWith('SET_TOKEN', 'new-access-token')
      expect(context.commit).toHaveBeenCalledWith('SET_REFRESH_TOKEN', 'new-refresh-token')
      expect(result).toBe('new-access-token')
    })

    it('should handle refresh token failure', async () => {
      context.state.refreshToken = 'invalid-refresh-token'
      mockAPI.post.mockRejectedValue(new Error('Invalid refresh token'))

      try {
        await authModule.actions.refreshToken(context)
      } catch (error) {
        expect(context.dispatch).toHaveBeenCalledWith('logout')
      }
    })

    it('should fetch user profile', async () => {
      const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>' }
      mockAPI.get.mockResolvedValue({ data: mockUser })

      context.state.token = 'valid-token'

      const result = await authModule.actions.fetchUser(context)

      expect(mockAPI.get).toHaveBeenCalledWith('/auth/me')
      expect(context.commit).toHaveBeenCalledWith('SET_USER', mockUser)
      expect(result).toEqual(mockUser)
    })
  })
})
