<template>
  <q-btn
    :class="buttonClasses"
    :color="color"
    :size="size"
    :flat="flat"
    :outline="outline"
    :unelevated="unelevated"
    :rounded="rounded"
    :push="push"
    :glossy="glossy"
    :fab="fab"
    :fab-mini="fabMini"
    :padding="padding"
    :loading="loading"
    :disable="disable"
    :ripple="ripple"
    :dense="dense"
    :round="round"
    :square="square"
    :stretch="stretch"
    :no-caps="noCaps"
    :no-wrap="noWrap"
    :align="align"
    :stack="stack"
    :icon="icon"
    :icon-right="iconRight"
    :label="label"
    :type="type"
    :to="to"
    :href="href"
    :target="target"
    @click="handleClick"
  >
    <slot />
    
    <!-- Loading slot -->
    <template v-if="loading" #loading>
      <q-spinner-hourglass class="on-left" />
      {{ loadingText || 'Loading...' }}
    </template>
  </q-btn>
</template>

<script>
export default {
  name: 'BaseButton',
  emits: ['click'],
  props: {
    // Content
    label: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    iconRight: {
      type: String,
      default: ''
    },
    
    // Appearance
    color: {
      type: String,
      default: 'primary'
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
    },
    
    // Style variants
    flat: {
      type: Boolean,
      default: false
    },
    outline: {
      type: Boolean,
      default: false
    },
    unelevated: {
      type: Boolean,
      default: false
    },
    rounded: {
      type: Boolean,
      default: false
    },
    push: {
      type: Boolean,
      default: false
    },
    glossy: {
      type: Boolean,
      default: false
    },
    
    // Shape
    fab: {
      type: Boolean,
      default: false
    },
    fabMini: {
      type: Boolean,
      default: false
    },
    round: {
      type: Boolean,
      default: false
    },
    square: {
      type: Boolean,
      default: false
    },
    
    // Layout
    padding: {
      type: String,
      default: ''
    },
    dense: {
      type: Boolean,
      default: false
    },
    stretch: {
      type: Boolean,
      default: false
    },
    stack: {
      type: Boolean,
      default: false
    },
    align: {
      type: String,
      default: 'center',
      validator: (value) => ['left', 'right', 'center', 'around', 'between', 'evenly'].includes(value)
    },
    
    // Text
    noCaps: {
      type: Boolean,
      default: false
    },
    noWrap: {
      type: Boolean,
      default: false
    },
    
    // State
    loading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: ''
    },
    disable: {
      type: Boolean,
      default: false
    },
    
    // Behavior
    ripple: {
      type: [Boolean, Object],
      default: true
    },
    type: {
      type: String,
      default: 'button',
      validator: (value) => ['button', 'submit', 'reset'].includes(value)
    },
    
    // Navigation
    to: {
      type: [String, Object],
      default: null
    },
    href: {
      type: String,
      default: ''
    },
    target: {
      type: String,
      default: ''
    },
    
    // Custom variants
    variant: {
      type: String,
      default: '',
      validator: (value) => ['', 'success', 'warning', 'danger', 'info'].includes(value)
    }
  },
  
  computed: {
    buttonClasses() {
      return [
        'base-button',
        {
          [`base-button--${this.variant}`]: this.variant,
          'base-button--loading': this.loading
        }
      ]
    }
  },
  
  methods: {
    handleClick(event) {
      if (!this.loading && !this.disable) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.base-button {
  &--success {
    background-color: #10b981;
    color: white;
    
    &:hover {
      background-color: #059669;
    }
  }
  
  &--warning {
    background-color: #f59e0b;
    color: white;
    
    &:hover {
      background-color: #d97706;
    }
  }
  
  &--danger {
    background-color: #ef4444;
    color: white;
    
    &:hover {
      background-color: #dc2626;
    }
  }
  
  &--info {
    background-color: #3b82f6;
    color: white;
    
    &:hover {
      background-color: #2563eb;
    }
  }
  
  &--loading {
    pointer-events: none;
  }
}
</style>
