<template>
  <div class="loading-spinner" :class="{ 'loading-spinner--overlay': overlay }">
    <div class="loading-spinner__container">
      <q-spinner
        :color="color"
        :size="size"
        :thickness="thickness"
      />
      <div v-if="message" class="loading-spinner__message">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    // Spinner appearance
    color: {
      type: String,
      default: 'primary'
    },
    size: {
      type: [String, Number],
      default: '40px'
    },
    thickness: {
      type: Number,
      default: 4
    },
    
    // Message
    message: {
      type: String,
      default: ''
    },
    
    // Overlay mode
    overlay: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  
  &--overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    backdrop-filter: blur(2px);
  }
  
  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
  
  &__message {
    font-size: 14px;
    color: #666;
    text-align: center;
    max-width: 200px;
  }
}
</style>
