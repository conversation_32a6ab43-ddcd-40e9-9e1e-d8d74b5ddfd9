<template>
  <div class="base-data-table">
    <q-table
      :rows="rows"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :selected="selected"
      :selection="selection"
      :filter="filter"
      :visible-columns="visibleColumns"
      :row-key="rowKey"
      :separator="separator"
      :dense="dense"
      :flat="flat"
      :bordered="bordered"
      :square="square"
      :no-data-label="noDataLabel"
      :no-results-label="noResultsLabel"
      :loading-label="loadingLabel"
      :rows-per-page-label="rowsPerPageLabel"
      :binary-state-sort="binaryStateSort"
      :column-sort-order="columnSortOrder"
      @update:pagination="handlePaginationUpdate"
      @update:selected="handleSelectionUpdate"
      @request="handleRequest"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDoubleClick"
    >
      <!-- Top slot for search and actions -->
      <template #top>
        <div class="base-data-table__top">
          <div class="base-data-table__title">
            <slot name="title">
              <div v-if="title" class="text-h6">{{ title }}</div>
            </slot>
          </div>
          
          <div class="base-data-table__actions">
            <!-- Search -->
            <q-input
              v-if="searchable"
              v-model="searchQuery"
              :placeholder="searchPlaceholder"
              outlined
              dense
              clearable
              class="base-data-table__search"
            >
              <template #prepend>
                <q-icon name="search" />
              </template>
            </q-input>
            
            <!-- Custom actions -->
            <slot name="actions" />
            
            <!-- Column visibility toggle -->
            <q-btn
              v-if="showColumnToggle"
              icon="view_column"
              flat
              round
              dense
            >
              <q-popup-proxy>
                <q-list>
                  <q-item
                    v-for="col in toggleableColumns"
                    :key="col.name"
                    tag="label"
                    clickable
                  >
                    <q-item-section avatar>
                      <q-checkbox
                        :model-value="visibleColumns.includes(col.name)"
                        @update:model-value="toggleColumn(col.name)"
                      />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ col.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-popup-proxy>
            </q-btn>
          </div>
        </div>
      </template>
      
      <!-- Custom column slots -->
      <template
        v-for="column in columns"
        :key="column.name"
        #[`body-cell-${column.name}`]="props"
      >
        <slot
          :name="`cell-${column.name}`"
          :row="props.row"
          :value="props.value"
          :col="props.col"
        >
          <q-td :props="props">
            {{ props.value }}
          </q-td>
        </slot>
      </template>
      
      <!-- Actions column -->
      <template #body-cell-actions="props">
        <q-td :props="props">
          <slot name="row-actions" :row="props.row">
            <q-btn
              icon="more_vert"
              flat
              round
              dense
              size="sm"
            >
              <q-menu>
                <q-list>
                  <q-item
                    v-for="action in rowActions"
                    :key="action.name"
                    clickable
                    @click="handleRowAction(action, props.row)"
                  >
                    <q-item-section avatar>
                      <q-icon :name="action.icon" />
                    </q-item-section>
                    <q-item-section>{{ action.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </slot>
        </q-td>
      </template>
      
      <!-- No data slot -->
      <template #no-data>
        <slot name="no-data">
          <div class="base-data-table__no-data">
            <q-icon name="inbox" size="48px" color="grey-4" />
            <div class="text-body1 q-mt-md">{{ noDataLabel }}</div>
          </div>
        </slot>
      </template>
      
      <!-- Loading slot -->
      <template #loading>
        <slot name="loading">
          <div class="base-data-table__loading">
            <q-spinner color="primary" size="40px" />
            <div class="text-body2 q-mt-md">{{ loadingLabel }}</div>
          </div>
        </slot>
      </template>
    </q-table>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'BaseDataTable',
  emits: [
    'update:pagination',
    'update:selected',
    'request',
    'row-click',
    'row-dblclick',
    'row-action'
  ],
  props: {
    // Data
    rows: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    
    // Table configuration
    title: {
      type: String,
      default: ''
    },
    rowKey: {
      type: String,
      default: 'id'
    },
    loading: {
      type: Boolean,
      default: false
    },
    
    // Pagination
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        rowsPerPage: 10,
        rowsNumber: 0
      })
    },
    
    // Selection
    selection: {
      type: String,
      default: 'none',
      validator: (value) => ['none', 'single', 'multiple'].includes(value)
    },
    selected: {
      type: Array,
      default: () => []
    },
    
    // Search
    searchable: {
      type: Boolean,
      default: true
    },
    searchPlaceholder: {
      type: String,
      default: 'Search...'
    },
    filter: {
      type: String,
      default: ''
    },
    
    // Appearance
    dense: {
      type: Boolean,
      default: false
    },
    flat: {
      type: Boolean,
      default: false
    },
    bordered: {
      type: Boolean,
      default: true
    },
    square: {
      type: Boolean,
      default: false
    },
    separator: {
      type: String,
      default: 'horizontal',
      validator: (value) => ['horizontal', 'vertical', 'cell', 'none'].includes(value)
    },
    
    // Column visibility
    showColumnToggle: {
      type: Boolean,
      default: true
    },
    
    // Row actions
    rowActions: {
      type: Array,
      default: () => []
    },
    
    // Labels
    noDataLabel: {
      type: String,
      default: 'No data available'
    },
    noResultsLabel: {
      type: String,
      default: 'No matching records found'
    },
    loadingLabel: {
      type: String,
      default: 'Loading...'
    },
    rowsPerPageLabel: {
      type: String,
      default: 'Rows per page:'
    },
    
    // Sorting
    binaryStateSort: {
      type: Boolean,
      default: false
    },
    columnSortOrder: {
      type: String,
      default: 'ad',
      validator: (value) => ['ad', 'da'].includes(value)
    }
  },
  
  setup(props, { emit }) {
    const searchQuery = ref('')
    const visibleColumns = ref([])
    
    // Initialize visible columns
    const initializeVisibleColumns = () => {
      visibleColumns.value = props.columns
        .filter(col => col.name !== 'actions')
        .map(col => col.name)
    }
    
    // Toggleable columns (exclude required columns)
    const toggleableColumns = computed(() => {
      return props.columns.filter(col => 
        col.name !== 'actions' && !col.required
      )
    })
    
    // Watch for search query changes
    watch(searchQuery, (newValue) => {
      emit('update:filter', newValue)
    })
    
    // Methods
    const handlePaginationUpdate = (pagination) => {
      emit('update:pagination', pagination)
    }
    
    const handleSelectionUpdate = (selected) => {
      emit('update:selected', selected)
    }
    
    const handleRequest = (requestProp) => {
      emit('request', requestProp)
    }
    
    const handleRowClick = (event, row) => {
      emit('row-click', { event, row })
    }
    
    const handleRowDoubleClick = (event, row) => {
      emit('row-dblclick', { event, row })
    }
    
    const handleRowAction = (action, row) => {
      emit('row-action', { action, row })
    }
    
    const toggleColumn = (columnName) => {
      const index = visibleColumns.value.indexOf(columnName)
      if (index > -1) {
        visibleColumns.value.splice(index, 1)
      } else {
        visibleColumns.value.push(columnName)
      }
    }
    
    // Initialize
    initializeVisibleColumns()
    
    return {
      searchQuery,
      visibleColumns,
      toggleableColumns,
      handlePaginationUpdate,
      handleSelectionUpdate,
      handleRequest,
      handleRowClick,
      handleRowDoubleClick,
      handleRowAction,
      toggleColumn
    }
  }
}
</script>

<style lang="scss" scoped>
.base-data-table {
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    gap: 16px;
  }
  
  &__title {
    flex: 1;
  }
  
  &__actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  &__search {
    min-width: 250px;
  }
  
  &__no-data,
  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    color: #666;
  }
}
</style>
