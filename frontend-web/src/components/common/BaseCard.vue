<template>
  <q-card
    :class="cardClasses"
    :flat="flat"
    :bordered="bordered"
    :square="square"
  >
    <!-- Header -->
    <q-card-section
      v-if="title || subtitle || $slots.header"
      class="base-card__header"
      :class="headerClasses"
    >
      <div v-if="$slots.header">
        <slot name="header" />
      </div>
      <div v-else>
        <div v-if="title" class="base-card__title">
          {{ title }}
        </div>
        <div v-if="subtitle" class="base-card__subtitle">
          {{ subtitle }}
        </div>
      </div>
      
      <!-- Header actions -->
      <div v-if="$slots.actions" class="base-card__actions">
        <slot name="actions" />
      </div>
    </q-card-section>
    
    <!-- Content -->
    <q-card-section
      v-if="$slots.default"
      class="base-card__content"
      :class="contentClasses"
    >
      <slot />
    </q-card-section>
    
    <!-- Footer -->
    <q-card-section
      v-if="$slots.footer"
      class="base-card__footer"
      :class="footerClasses"
    >
      <slot name="footer" />
    </q-card-section>
  </q-card>
</template>

<script>
export default {
  name: 'BaseCard',
  props: {
    // Card appearance
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    flat: {
      type: Boolean,
      default: false
    },
    bordered: {
      type: Boolean,
      default: true
    },
    square: {
      type: Boolean,
      default: false
    },
    
    // Styling
    headerClass: {
      type: [String, Array, Object],
      default: ''
    },
    contentClass: {
      type: [String, Array, Object],
      default: ''
    },
    footerClass: {
      type: [String, Array, Object],
      default: ''
    },
    
    // Layout
    noPadding: {
      type: Boolean,
      default: false
    },
    dense: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    cardClasses() {
      return [
        'base-card',
        {
          'base-card--no-padding': this.noPadding,
          'base-card--dense': this.dense
        }
      ]
    },
    
    headerClasses() {
      return [
        'base-card__header',
        this.headerClass,
        {
          'base-card__header--dense': this.dense
        }
      ]
    },
    
    contentClasses() {
      return [
        'base-card__content',
        this.contentClass,
        {
          'base-card__content--no-padding': this.noPadding,
          'base-card__content--dense': this.dense
        }
      ]
    },
    
    footerClasses() {
      return [
        'base-card__footer',
        this.footerClass,
        {
          'base-card__footer--dense': this.dense
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.base-card {
  &--no-padding {
    .base-card__content {
      padding: 0;
    }
  }
  
  &--dense {
    .base-card__header,
    .base-card__content,
    .base-card__footer {
      padding: 12px 16px;
    }
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e0e0e0;
    
    &--dense {
      padding: 8px 12px;
    }
  }
  
  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
  }
  
  &__subtitle {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
  }
  
  &__actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  &__content {
    &--no-padding {
      padding: 0;
    }
    
    &--dense {
      padding: 12px 16px;
    }
  }
  
  &__footer {
    border-top: 1px solid #e0e0e0;
    
    &--dense {
      padding: 8px 12px;
    }
  }
}
</style>
