<template>
  <q-dialog
    v-model="isVisible"
    :persistent="persistent"
    :maximized="maximized"
    :full-width="fullWidth"
    :full-height="fullHeight"
    :position="position"
    :transition-show="transitionShow"
    :transition-hide="transitionHide"
    @hide="handleHide"
    @show="handleShow"
  >
    <q-card :class="cardClasses" :style="cardStyle">
      <!-- Header -->
      <q-card-section
        v-if="title || subtitle || $slots.header"
        class="base-dialog__header"
        :class="headerClasses"
      >
        <div v-if="$slots.header">
          <slot name="header" />
        </div>
        <div v-else class="base-dialog__header-content">
          <div>
            <div v-if="title" class="base-dialog__title">
              {{ title }}
            </div>
            <div v-if="subtitle" class="base-dialog__subtitle">
              {{ subtitle }}
            </div>
          </div>
          
          <q-btn
            v-if="showCloseButton"
            icon="close"
            flat
            round
            dense
            @click="close"
          />
        </div>
      </q-card-section>
      
      <!-- Content -->
      <q-card-section
        class="base-dialog__content"
        :class="contentClasses"
      >
        <slot />
      </q-card-section>
      
      <!-- Actions -->
      <q-card-actions
        v-if="$slots.actions || showDefaultActions"
        class="base-dialog__actions"
        :class="actionsClasses"
        :align="actionsAlign"
      >
        <slot name="actions">
          <div v-if="showDefaultActions" class="base-dialog__default-actions">
            <q-btn
              v-if="showCancelButton"
              :label="cancelText"
              flat
              color="grey"
              @click="cancel"
            />
            <q-btn
              v-if="showConfirmButton"
              :label="confirmText"
              :color="confirmColor"
              :loading="confirmLoading"
              @click="confirm"
            />
          </div>
        </slot>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
export default {
  name: 'BaseDialog',
  emits: ['update:modelValue', 'hide', 'show', 'confirm', 'cancel'],
  props: {
    // Visibility
    modelValue: {
      type: Boolean,
      default: false
    },
    
    // Content
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    
    // Behavior
    persistent: {
      type: Boolean,
      default: false
    },
    showCloseButton: {
      type: Boolean,
      default: true
    },
    
    // Size and position
    maximized: {
      type: Boolean,
      default: false
    },
    fullWidth: {
      type: Boolean,
      default: false
    },
    fullHeight: {
      type: Boolean,
      default: false
    },
    width: {
      type: [String, Number],
      default: ''
    },
    maxWidth: {
      type: [String, Number],
      default: '500px'
    },
    position: {
      type: String,
      default: 'standard',
      validator: (value) => ['standard', 'top', 'right', 'bottom', 'left'].includes(value)
    },
    
    // Transitions
    transitionShow: {
      type: String,
      default: 'scale'
    },
    transitionHide: {
      type: String,
      default: 'scale'
    },
    
    // Styling
    headerClass: {
      type: [String, Array, Object],
      default: ''
    },
    contentClass: {
      type: [String, Array, Object],
      default: ''
    },
    actionsClass: {
      type: [String, Array, Object],
      default: ''
    },
    
    // Actions
    showDefaultActions: {
      type: Boolean,
      default: false
    },
    showCancelButton: {
      type: Boolean,
      default: true
    },
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: 'Cancel'
    },
    confirmText: {
      type: String,
      default: 'Confirm'
    },
    confirmColor: {
      type: String,
      default: 'primary'
    },
    confirmLoading: {
      type: Boolean,
      default: false
    },
    actionsAlign: {
      type: String,
      default: 'right',
      validator: (value) => ['left', 'center', 'right', 'around', 'between', 'evenly'].includes(value)
    }
  },
  
  computed: {
    isVisible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    
    cardClasses() {
      return [
        'base-dialog__card',
        {
          'base-dialog__card--maximized': this.maximized
        }
      ]
    },
    
    cardStyle() {
      const style = {}
      
      if (this.width) {
        style.width = typeof this.width === 'number' ? `${this.width}px` : this.width
      }
      
      if (this.maxWidth && !this.maximized) {
        style.maxWidth = typeof this.maxWidth === 'number' ? `${this.maxWidth}px` : this.maxWidth
      }
      
      return style
    },
    
    headerClasses() {
      return [
        'base-dialog__header',
        this.headerClass
      ]
    },
    
    contentClasses() {
      return [
        'base-dialog__content',
        this.contentClass
      ]
    },
    
    actionsClasses() {
      return [
        'base-dialog__actions',
        this.actionsClass
      ]
    }
  },
  
  methods: {
    close() {
      this.isVisible = false
    },
    
    confirm() {
      this.$emit('confirm')
    },
    
    cancel() {
      this.$emit('cancel')
      this.close()
    },
    
    handleHide() {
      this.$emit('hide')
    },
    
    handleShow() {
      this.$emit('show')
    }
  }
}
</script>

<style lang="scss" scoped>
.base-dialog {
  &__card {
    min-width: 300px;
    
    &--maximized {
      width: 100vw;
      height: 100vh;
      max-width: none;
      max-height: none;
    }
  }
  
  &__header {
    border-bottom: 1px solid #e0e0e0;
    
    &-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      width: 100%;
    }
  }
  
  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
  }
  
  &__subtitle {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
  }
  
  &__content {
    max-height: 70vh;
    overflow-y: auto;
  }
  
  &__actions {
    border-top: 1px solid #e0e0e0;
    padding: 16px 24px;
  }
  
  &__default-actions {
    display: flex;
    gap: 8px;
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
