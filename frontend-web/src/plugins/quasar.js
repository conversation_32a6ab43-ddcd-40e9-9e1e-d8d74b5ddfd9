/**
 * Smart Factory WMS - Quasar Framework Configuration
 * 
 * This file configures Quasar Framework with custom theme,
 * components, and plugins for the Smart Factory WMS application.
 */

import { Quasar, Notify, Dialog, Loading, LocalStorage, SessionStorage } from 'quasar'

// Import Quasar css
import 'quasar/src/css/index.sass'

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css'
import '@quasar/extras/material-icons-outlined/material-icons-outlined.css'
import '@quasar/extras/material-icons-round/material-icons-round.css'
import '@quasar/extras/material-icons-sharp/material-icons-sharp.css'
import '@quasar/extras/fontawesome-v6/fontawesome-v6.css'

// Custom theme configuration
const customTheme = {
  primary: '#1976d2',
  secondary: '#26a69a',
  accent: '#9c27b0',
  dark: '#1d1d1d',
  positive: '#21ba45',
  negative: '#c10015',
  info: '#31ccec',
  warning: '#f2c037'
}

// Quasar configuration
const quasarConfig = {
  config: {
    brand: customTheme,
    notify: {
      position: 'top-right',
      timeout: 5000,
      textColor: 'white',
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ]
    },
    loading: {
      delay: 200,
      message: 'Loading...',
      spinnerSize: 80,
      spinnerColor: 'primary'
    },
    dialog: {
      ok: {
        color: 'primary',
        flat: true
      },
      cancel: {
        color: 'grey',
        flat: true
      }
    }
  },
  plugins: {
    Notify,
    Dialog,
    Loading,
    LocalStorage,
    SessionStorage
  },
  extras: [
    'material-icons',
    'material-icons-outlined',
    'material-icons-round',
    'material-icons-sharp',
    'fontawesome-v6'
  ]
}

// Custom notification presets
export const notifications = {
  success(message, options = {}) {
    return Notify.create({
      type: 'positive',
      message,
      icon: 'check_circle',
      position: 'top-right',
      timeout: 3000,
      ...options
    })
  },
  
  error(message, options = {}) {
    return Notify.create({
      type: 'negative',
      message,
      icon: 'error',
      position: 'top-right',
      timeout: 6000,
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ],
      ...options
    })
  },
  
  warning(message, options = {}) {
    return Notify.create({
      type: 'warning',
      message,
      icon: 'warning',
      position: 'top-right',
      timeout: 5000,
      ...options
    })
  },
  
  info(message, options = {}) {
    return Notify.create({
      type: 'info',
      message,
      icon: 'info',
      position: 'top-right',
      timeout: 4000,
      ...options
    })
  },
  
  // Authentication specific notifications
  authSuccess(message = 'Authentication successful') {
    return this.success(message, {
      timeout: 2000,
      icon: 'login'
    })
  },
  
  authError(message = 'Authentication failed') {
    return this.error(message, {
      timeout: 6000,
      icon: 'lock'
    })
  },
  
  // Network error notification
  networkError(message = 'Network error. Please check your connection.') {
    return this.error(message, {
      timeout: 8000,
      icon: 'wifi_off'
    })
  }
}

// Custom dialog presets
export const dialogs = {
  confirm(title, message, options = {}) {
    return Dialog.create({
      title,
      message,
      ok: {
        label: 'Confirm',
        color: 'primary',
        flat: true
      },
      cancel: {
        label: 'Cancel',
        color: 'grey',
        flat: true
      },
      persistent: true,
      ...options
    })
  },
  
  alert(title, message, options = {}) {
    return Dialog.create({
      title,
      message,
      ok: {
        label: 'OK',
        color: 'primary',
        flat: true
      },
      ...options
    })
  },
  
  prompt(title, message, options = {}) {
    return Dialog.create({
      title,
      message,
      prompt: {
        model: '',
        type: 'text'
      },
      ok: {
        label: 'Submit',
        color: 'primary',
        flat: true
      },
      cancel: {
        label: 'Cancel',
        color: 'grey',
        flat: true
      },
      persistent: true,
      ...options
    })
  }
}

// Loading utilities
export const loading = {
  show(message = 'Loading...', options = {}) {
    Loading.show({
      message,
      spinnerSize: 80,
      spinnerColor: 'primary',
      messageColor: 'black',
      backgroundColor: 'white',
      ...options
    })
  },
  
  hide() {
    Loading.hide()
  },
  
  // Async wrapper that shows loading during promise execution
  async wrap(promise, message = 'Loading...') {
    this.show(message)
    try {
      const result = await promise
      return result
    } finally {
      this.hide()
    }
  }
}

// Storage utilities
export const storage = {
  local: {
    set(key, value) {
      return LocalStorage.set(key, value)
    },
    
    get(key, defaultValue = null) {
      return LocalStorage.getItem(key) || defaultValue
    },
    
    remove(key) {
      return LocalStorage.remove(key)
    },
    
    clear() {
      return LocalStorage.clear()
    },
    
    has(key) {
      return LocalStorage.has(key)
    }
  },
  
  session: {
    set(key, value) {
      return SessionStorage.set(key, value)
    },
    
    get(key, defaultValue = null) {
      return SessionStorage.getItem(key) || defaultValue
    },
    
    remove(key) {
      return SessionStorage.remove(key)
    },
    
    clear() {
      return SessionStorage.clear()
    },
    
    has(key) {
      return SessionStorage.has(key)
    }
  }
}

// Plugin installation function
export default {
  install(app, options = {}) {
    // Merge custom options with defaults
    const finalConfig = { ...quasarConfig, ...options }
    
    // Install Quasar
    app.use(Quasar, finalConfig)
    
    // Add utilities to global properties
    app.config.globalProperties.$notify = notifications
    app.config.globalProperties.$dialogs = dialogs
    app.config.globalProperties.$loading = loading
    app.config.globalProperties.$storage = storage
    
    // Provide utilities for composition API
    app.provide('notify', notifications)
    app.provide('dialogs', dialogs)
    app.provide('loading', loading)
    app.provide('storage', storage)
  }
}
