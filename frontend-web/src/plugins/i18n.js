/**
 * Smart Factory WMS - Vue I18n Configuration
 * 
 * This file configures Vue I18n for internationalization support
 * with multiple languages and locale-specific formatting.
 */

import { createI18n } from 'vue-i18n'

// Import language files
import en from '@/i18n/locales/en.js'
import ja from '@/i18n/locales/ja.js'
import zh from '@/i18n/locales/zh.js'
import vi from '@/i18n/locales/vi.js'

// Get saved language from localStorage or default to English
const savedLanguage = localStorage.getItem('ui_preferences')
let defaultLocale = 'en'

if (savedLanguage) {
  try {
    const preferences = JSON.parse(savedLanguage)
    if (preferences.language && ['en', 'ja', 'zh', 'vi'].includes(preferences.language)) {
      defaultLocale = preferences.language
    }
  } catch (error) {
    console.warn('Failed to parse saved language preference:', error)
  }
}

// Create i18n instance
const i18n = createI18n({
  legacy: false, // Use Composition API mode
  locale: defaultLocale,
  fallbackLocale: 'en',
  globalInjection: true,
  messages: {
    en,
    ja,
    zh,
    vi
  },
  datetimeFormats: {
    en: {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    ja: {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    zh: {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    vi: {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    }
  },
  numberFormats: {
    en: {
      currency: {
        style: 'currency',
        currency: 'USD'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    ja: {
      currency: {
        style: 'currency',
        currency: 'JPY'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    zh: {
      currency: {
        style: 'currency',
        currency: 'CNY'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    vi: {
      currency: {
        style: 'currency',
        currency: 'VND'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }
    }
  }
})

export default i18n
