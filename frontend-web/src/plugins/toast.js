/**
 * Smart Factory WMS - Toast Notification Plugin
 * 
 * This plugin provides toast notification functionality using vue-toastification
 * with custom configuration for the Smart Factory WMS application.
 */

import { createApp } from 'vue'
import Toast, { POSITION } from 'vue-toastification'
import 'vue-toastification/dist/index.css'

// Custom toast configuration
const toastOptions = {
  position: POSITION.TOP_RIGHT,
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false,
  transition: 'Vue-Toastification__bounce',
  maxToasts: 5,
  newestOnTop: true,
  filterBeforeCreate: (toast, toasts) => {
    // Prevent duplicate toasts
    if (toasts.filter(t => t.content === toast.content).length !== 0) {
      return false
    }
    return toast
  },
  toastDefaults: {
    // Default options for each toast type
    [Toast.TYPE.SUCCESS]: {
      timeout: 3000,
      hideProgressBar: true
    },
    [Toast.TYPE.ERROR]: {
      timeout: 8000,
      hideProgressBar: false
    },
    [Toast.TYPE.WARNING]: {
      timeout: 5000,
      hideProgressBar: false
    },
    [Toast.TYPE.INFO]: {
      timeout: 4000,
      hideProgressBar: true
    }
  }
}

// Custom CSS for toast styling
const customCSS = `
.Vue-Toastification__toast {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.Vue-Toastification__toast--success {
  background-color: #10b981;
}

.Vue-Toastification__toast--error {
  background-color: #ef4444;
}

.Vue-Toastification__toast--warning {
  background-color: #f59e0b;
}

.Vue-Toastification__toast--info {
  background-color: #3b82f6;
}

.Vue-Toastification__progress-bar {
  height: 3px;
}

.Vue-Toastification__toast-body {
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
}

.Vue-Toastification__close-button {
  opacity: 0.8;
  font-size: 18px;
  font-weight: bold;
}

.Vue-Toastification__close-button:hover {
  opacity: 1;
}
`

// Inject custom CSS
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = customCSS
  document.head.appendChild(style)
}

// Toast utility functions
export const toast = {
  success(message, options = {}) {
    return Toast.success(message, options)
  },
  
  error(message, options = {}) {
    return Toast.error(message, options)
  },
  
  warning(message, options = {}) {
    return Toast.warning(message, options)
  },
  
  info(message, options = {}) {
    return Toast.info(message, options)
  },
  
  // Custom toast for authentication events
  authSuccess(message = 'Authentication successful') {
    return Toast.success(message, {
      timeout: 2000,
      hideProgressBar: true
    })
  },
  
  authError(message = 'Authentication failed') {
    return Toast.error(message, {
      timeout: 6000,
      hideProgressBar: false
    })
  },
  
  // Custom toast for API operations
  apiSuccess(message = 'Operation completed successfully') {
    return Toast.success(message, {
      timeout: 3000,
      hideProgressBar: true
    })
  },
  
  apiError(message = 'Operation failed') {
    return Toast.error(message, {
      timeout: 7000,
      hideProgressBar: false
    })
  },
  
  // Network error toast
  networkError(message = 'Network error. Please check your connection.') {
    return Toast.error(message, {
      timeout: 8000,
      hideProgressBar: false
    })
  },
  
  // Loading toast (returns toast instance for manual dismissal)
  loading(message = 'Loading...') {
    return Toast.info(message, {
      timeout: false,
      hideProgressBar: true,
      closeOnClick: false,
      draggable: false
    })
  },
  
  // Dismiss specific toast
  dismiss(toastId) {
    Toast.dismiss(toastId)
  },
  
  // Clear all toasts
  clear() {
    Toast.clear()
  }
}

// Plugin installation function
export default {
  install(app, options = {}) {
    // Merge custom options with defaults
    const finalOptions = { ...toastOptions, ...options }
    
    // Install vue-toastification
    app.use(Toast, finalOptions)
    
    // Add toast utilities to global properties
    app.config.globalProperties.$toast = toast
    
    // Provide toast for composition API
    app.provide('toast', toast)
  }
}
