/**
 * English Language File
 * Smart Factory WMS Frontend
 */

export default {
  // Application
  app: {
    name: "Smart Factory WMS",
    version: "1.0.0",
    loading: "Loading...",
    saving: "Saving...",
    saved: "Saved successfully",
    error: "An error occurred",
    success: "Operation completed successfully",
    confirm: "Are you sure?",
    cancel: "Cancel",
    ok: "OK",
    yes: "Yes",
    no: "No",
    close: "Close",
    back: "Back",
    next: "Next",
    previous: "Previous",
    search: "Search",
    filter: "Filter",
    clear: "Clear",
    reset: "Reset",
    refresh: "Refresh",
    export: "Export",
    import: "Import",
    print: "Print",
    download: "Download",
    upload: "Upload",
    edit: "Edit",
    delete: "Delete",
    create: "Create",
    update: "Update",
    view: "View",
    select: "Select",
    required: "Required",
    optional: "Optional",
  },

  // Authentication
  auth: {
    title: "Smart Factory WMS",
    welcome_message: "Welcome to Smart Factory WMS",
    login: "Sign In",
    logout: "Sign Out",
    register: "Register",
    username_label: "Username or Email",
    username_placeholder: "Enter your username or email",
    password_label: "Password",
    password_placeholder: "Enter your password",
    remember_me: "Remember me",
    sign_in_button: "Sign In",
    forgot_password: "Forgot Password?",
    language_label: "Language",
    loading_message: "Signing in...",

    // Validation messages
    validation: {
      username_required: "Username or email is required",
      username_min_length: "Username must be at least 3 characters",
      username_max_length: "Username must not exceed 50 characters",
      username_invalid:
        "Username can only contain letters, numbers, dots, and underscores",
      password_required: "Password is required",
      password_min_length: "Password must be at least 8 characters",
      password_max_length: "Password must not exceed 128 characters",
    },

    // Error messages
    errors: {
      invalid_credentials: "Invalid username or password",
      account_locked: "Account is locked. Please contact administrator.",
      account_disabled: "Account is disabled. Please contact administrator.",
      too_many_requests: "Too many login attempts. Please try again later.",
      try_again_in: "Try again in",
      network_error:
        "Network error. Please check your connection and try again.",
      server_error: "Server error. Please try again later.",
      session_expired: "Your session has expired. Please sign in again.",
      unauthorized: "You are not authorized to access this resource.",
      forbidden:
        "Access denied. You do not have permission to perform this action.",
      token_invalid: "Invalid authentication token. Please sign in again.",
      token_expired: "Authentication token has expired. Please sign in again.",
    },

    // Success messages
    success: {
      login: "Successfully signed in",
      logout: "Successfully signed out",
      password_reset_sent:
        "Password reset instructions have been sent to your email",
      password_changed: "Password changed successfully",
    },
  },

  // Navigation
  menu: {
    dashboard: "Dashboard",
    inventory: "Inventory",
    receiving: "Receiving",
    shipment: "Shipment",
    production: "Production",
    reports: "Reports",
    users: "Users",
    settings: "Settings",
    profile: "Profile",
    help: "Help",
    about: "About",
  },

  // Common form elements
  form: {
    save: "Save",
    cancel: "Cancel",
    submit: "Submit",
    reset: "Reset",
    clear: "Clear",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    actions: "Actions",
    status: "Status",
    active: "Active",
    inactive: "Inactive",
    enabled: "Enabled",
    disabled: "Disabled",
    name: "Name",
    description: "Description",
    created_at: "Created At",
    updated_at: "Updated At",
    created_by: "Created By",
    updated_by: "Updated By",
  },

  // Error pages
  errors: {
    not_found: "Page Not Found",
    not_found_message: "The page you are looking for does not exist.",
    unauthorized: "Unauthorized",
    unauthorized_message: "You are not authorized to access this page.",
    server_error: "Server Error",
    server_error_message:
      "An internal server error occurred. Please try again later.",
    network_error: "Network Error",
    network_error_message:
      "Unable to connect to the server. Please check your internet connection.",
    go_home: "Go to Dashboard",
    try_again: "Try Again",
    contact_support: "Contact Support",
  },

  // Languages
  languages: {
    en: "English",
    ja: "日本語",
    zh: "中文",
    vi: "Tiếng Việt",
  },

  // Date and time
  datetime: {
    now: "Now",
    today: "Today",
    yesterday: "Yesterday",
    tomorrow: "Tomorrow",
    this_week: "This Week",
    last_week: "Last Week",
    this_month: "This Month",
    last_month: "Last Month",
    this_year: "This Year",
    last_year: "Last Year",
  },
};
