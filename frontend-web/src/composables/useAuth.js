/**
 * Smart Factory WMS - Authentication Composable
 * 
 * This composable provides authentication functionality including
 * form validation, login/logout actions, and error handling.
 */

import { ref, reactive, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

export function useAuth() {
  const store = useStore()
  const router = useRouter()
  const { t } = useI18n()

  // Form state
  const loginForm = reactive({
    username: '',
    password: '',
    rememberMe: false
  })

  // UI state
  const showPassword = ref(false)
  const isSubmitting = ref(false)
  const submitAttempted = ref(false)

  // Error state
  const errors = reactive({
    username: '',
    password: '',
    general: ''
  })

  // Computed properties
  const isLoading = computed(() => store.state.auth.isLoading || isSubmitting.value)
  const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
  const user = computed(() => store.state.auth.user)
  const loginAttempts = computed(() => store.state.auth.loginAttempts)

  // Form validation rules
  const validationRules = {
    username: [
      {
        test: (value) => !!value?.trim(),
        message: () => t('auth.validation.username_required')
      },
      {
        test: (value) => !value || value.length >= 3,
        message: () => t('auth.validation.username_min_length')
      },
      {
        test: (value) => !value || value.length <= 50,
        message: () => t('auth.validation.username_max_length')
      },
      {
        test: (value) => !value || /^[a-zA-Z0-9._@-]+$/.test(value),
        message: () => t('auth.validation.username_invalid')
      }
    ],
    password: [
      {
        test: (value) => !!value,
        message: () => t('auth.validation.password_required')
      },
      {
        test: (value) => !value || value.length >= 8,
        message: () => t('auth.validation.password_min_length')
      },
      {
        test: (value) => !value || value.length <= 128,
        message: () => t('auth.validation.password_max_length')
      }
    ]
  }

  // Validation functions
  const validateField = (field, value) => {
    const rules = validationRules[field]
    if (!rules) return ''

    for (const rule of rules) {
      if (!rule.test(value)) {
        return rule.message()
      }
    }
    return ''
  }

  const validateForm = () => {
    const usernameError = validateField('username', loginForm.username)
    const passwordError = validateField('password', loginForm.password)

    errors.username = usernameError
    errors.password = passwordError
    errors.general = ''

    return !usernameError && !passwordError
  }

  // Watch for form changes to clear errors
  watch(() => loginForm.username, (newValue) => {
    if (submitAttempted.value) {
      errors.username = validateField('username', newValue)
    }
    if (errors.general) {
      errors.general = ''
    }
  })

  watch(() => loginForm.password, (newValue) => {
    if (submitAttempted.value) {
      errors.password = validateField('password', newValue)
    }
    if (errors.general) {
      errors.general = ''
    }
  })

  // Authentication actions
  const login = async () => {
    submitAttempted.value = true
    
    if (!validateForm()) {
      return { success: false, error: 'Validation failed' }
    }

    try {
      isSubmitting.value = true
      errors.general = ''

      const result = await store.dispatch('auth/login', {
        username: loginForm.username.trim(),
        password: loginForm.password,
        rememberMe: loginForm.rememberMe
      })

      if (result.success) {
        // Clear form
        resetForm()
        
        // Redirect to intended route or dashboard
        const redirectRoute = store.state.auth.redirectRoute || '/dashboard'
        store.commit('auth/CLEAR_REDIRECT_ROUTE')
        
        await router.push(redirectRoute)
        
        return { success: true, user: result.user }
      }

      return { success: false, error: 'Login failed' }
    } catch (error) {
      console.error('Login error:', error)
      
      // Map error to user-friendly message
      let errorMessage = t('auth.errors.network_error')
      
      if (error.code) {
        switch (error.code) {
          case 404:
            errorMessage = t('auth.errors.invalid_credentials')
            break
          case 401:
            errorMessage = t('auth.errors.invalid_credentials')
            break
          case 423:
            errorMessage = t('auth.errors.account_locked')
            break
          case 429:
            errorMessage = t('auth.errors.too_many_requests')
            break
          case 500:
            errorMessage = t('auth.errors.server_error')
            break
          default:
            errorMessage = error.message || t('auth.errors.network_error')
        }
      }
      
      errors.general = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isSubmitting.value = false
    }
  }

  const logout = async () => {
    try {
      await store.dispatch('auth/logout')
      await router.push('/login')
      return { success: true }
    } catch (error) {
      console.error('Logout error:', error)
      return { success: false, error: error.message }
    }
  }

  // Form utilities
  const resetForm = () => {
    loginForm.username = ''
    loginForm.password = ''
    loginForm.rememberMe = false
    errors.username = ''
    errors.password = ''
    errors.general = ''
    submitAttempted.value = false
  }

  const clearErrors = () => {
    errors.username = ''
    errors.password = ''
    errors.general = ''
  }

  const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value
  }

  // Check if user can attempt login (rate limiting)
  const canAttemptLogin = computed(() => {
    const maxAttempts = 5
    const lockoutTime = 15 * 60 * 1000 // 15 minutes
    
    if (loginAttempts.value < maxAttempts) return true
    
    const lastAttempt = store.state.auth.lastLoginAttempt
    if (!lastAttempt) return true
    
    return Date.now() - lastAttempt > lockoutTime
  })

  const getRemainingLockoutTime = computed(() => {
    const lockoutTime = 15 * 60 * 1000 // 15 minutes
    const lastAttempt = store.state.auth.lastLoginAttempt
    
    if (!lastAttempt) return 0
    
    const elapsed = Date.now() - lastAttempt
    return Math.max(0, lockoutTime - elapsed)
  })

  // Initialize form from store if available
  const initializeForm = () => {
    const savedForm = store.state.auth.loginForm
    if (savedForm) {
      loginForm.username = savedForm.username || ''
      loginForm.password = savedForm.password || ''
      loginForm.rememberMe = savedForm.rememberMe || false
    }
  }

  // Save form state to store
  const saveFormState = () => {
    store.dispatch('auth/updateLoginForm', {
      username: loginForm.username,
      password: '', // Don't persist password
      rememberMe: loginForm.rememberMe
    })
  }

  return {
    // Form state
    loginForm,
    showPassword,
    isSubmitting,
    submitAttempted,
    
    // Error state
    errors,
    
    // Computed properties
    isLoading,
    isAuthenticated,
    user,
    loginAttempts,
    canAttemptLogin,
    getRemainingLockoutTime,
    
    // Actions
    login,
    logout,
    
    // Form utilities
    resetForm,
    clearErrors,
    togglePasswordVisibility,
    validateField,
    validateForm,
    initializeForm,
    saveFormState
  }
}
