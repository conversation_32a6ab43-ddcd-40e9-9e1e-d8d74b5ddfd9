<template>
  <AuthLayout>
    <form @submit.prevent="handleLogin" class="login-form">
      <!-- Username/Email Field -->
      <div class="login-form__field">
        <q-input
          v-model="loginForm.username"
          :label="$t('auth.username_label')"
          :placeholder="$t('auth.username_placeholder')"
          :error="!!errors.username"
          :error-message="errors.username"
          outlined
          dense
          autocomplete="username"
          :disable="isLoading"
          class="login-form__input"
          @blur="validateUsername"
          @keyup.enter="handleLogin"
        >
          <template #prepend>
            <q-icon name="person" />
          </template>
        </q-input>
      </div>
      
      <!-- Password Field -->
      <div class="login-form__field">
        <q-input
          v-model="loginForm.password"
          :label="$t('auth.password_label')"
          :placeholder="$t('auth.password_placeholder')"
          :type="showPassword ? 'text' : 'password'"
          :error="!!errors.password"
          :error-message="errors.password"
          outlined
          dense
          autocomplete="current-password"
          :disable="isLoading"
          class="login-form__input"
          @blur="validatePassword"
          @keyup.enter="handleLogin"
        >
          <template #prepend>
            <q-icon name="lock" />
          </template>
          <template #append>
            <q-btn
              :icon="showPassword ? 'visibility_off' : 'visibility'"
              flat
              round
              dense
              size="sm"
              :disable="isLoading"
              @click="togglePasswordVisibility"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
            />
          </template>
        </q-input>
      </div>
      
      <!-- Remember Me -->
      <div class="login-form__field">
        <q-checkbox
          v-model="loginForm.rememberMe"
          :label="$t('auth.remember_me')"
          :disable="isLoading"
          class="login-form__checkbox"
        />
      </div>
      
      <!-- General Error Message -->
      <div v-if="errors.general" class="login-form__error">
        <q-banner
          inline-actions
          rounded
          class="bg-negative text-white"
        >
          <template #avatar>
            <q-icon name="error" />
          </template>
          {{ errors.general }}
          <template #action>
            <q-btn
              flat
              round
              dense
              icon="close"
              @click="clearErrors"
            />
          </template>
        </q-banner>
      </div>
      
      <!-- Rate Limiting Warning -->
      <div v-if="!canAttemptLogin" class="login-form__warning">
        <q-banner
          inline-actions
          rounded
          class="bg-warning text-white"
        >
          <template #avatar>
            <q-icon name="warning" />
          </template>
          {{ $t('auth.errors.too_many_requests') }}
          <br>
          <small>
            {{ $t('auth.errors.try_again_in') }} {{ formatRemainingTime(getRemainingLockoutTime) }}
          </small>
        </q-banner>
      </div>
      
      <!-- Submit Button -->
      <div class="login-form__field">
        <q-btn
          type="submit"
          :label="$t('auth.sign_in_button')"
          :loading="isLoading"
          :disable="!canAttemptLogin || isLoading"
          color="primary"
          size="lg"
          class="login-form__submit full-width"
          no-caps
        >
          <template #loading>
            <q-spinner-hourglass class="on-left" />
            {{ $t('auth.loading_message') }}
          </template>
        </q-btn>
      </div>
      
      <!-- Forgot Password Link -->
      <div class="login-form__links">
        <router-link
          to="/forgot-password"
          class="login-form__link"
          :class="{ 'login-form__link--disabled': isLoading }"
        >
          {{ $t('auth.forgot_password') }}
        </router-link>
      </div>
    </form>
  </AuthLayout>
</template>

<script>
import { computed, onMounted, onUnmounted } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useI18n } from 'vue-i18n'
import AuthLayout from '@/layouts/AuthLayout.vue'

export default {
  name: 'Login',
  components: {
    AuthLayout
  },
  setup() {
    const { t } = useI18n()
    const {
      loginForm,
      showPassword,
      isLoading,
      errors,
      canAttemptLogin,
      getRemainingLockoutTime,
      login,
      clearErrors,
      togglePasswordVisibility,
      validateField,
      initializeForm
    } = useAuth()
    
    // Countdown timer for rate limiting
    let countdownTimer = null
    
    const startCountdown = () => {
      if (countdownTimer) {
        clearInterval(countdownTimer)
      }
      
      countdownTimer = setInterval(() => {
        if (getRemainingLockoutTime.value <= 0) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      }, 1000)
    }
    
    const formatRemainingTime = (milliseconds) => {
      const minutes = Math.floor(milliseconds / 60000)
      const seconds = Math.floor((milliseconds % 60000) / 1000)
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
    
    const validateUsername = () => {
      errors.username = validateField('username', loginForm.username)
    }
    
    const validatePassword = () => {
      errors.password = validateField('password', loginForm.password)
    }
    
    const handleLogin = async () => {
      if (!canAttemptLogin.value) {
        return
      }
      
      // Clear previous errors
      clearErrors()
      
      // Validate form
      validateUsername()
      validatePassword()
      
      if (errors.username || errors.password) {
        return
      }
      
      try {
        const result = await login()
        
        if (result.success) {
          // Login successful - navigation is handled in the composable
          console.log('Login successful')
        } else {
          // Login failed - error is already set in the composable
          if (!canAttemptLogin.value) {
            startCountdown()
          }
        }
      } catch (error) {
        console.error('Login error:', error)
        if (!canAttemptLogin.value) {
          startCountdown()
        }
      }
    }
    
    // Initialize form and start countdown if needed
    onMounted(() => {
      initializeForm()
      
      if (!canAttemptLogin.value) {
        startCountdown()
      }
    })
    
    // Cleanup timer
    onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer)
      }
    })
    
    return {
      // Form state
      loginForm,
      showPassword,
      isLoading,
      errors,
      
      // Computed properties
      canAttemptLogin,
      getRemainingLockoutTime,
      
      // Methods
      handleLogin,
      clearErrors,
      togglePasswordVisibility,
      validateUsername,
      validatePassword,
      formatRemainingTime
    }
  }
}
</script>

<style lang="scss" scoped>
.login-form {
  width: 100%;
  
  &__field {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__input {
    .q-field__control {
      border-radius: 8px;
    }
    
    .q-field__prepend {
      color: #666;
    }
  }
  
  &__checkbox {
    .q-checkbox__label {
      font-size: 14px;
      color: #666;
    }
  }
  
  &__error,
  &__warning {
    margin-bottom: 16px;
    
    .q-banner {
      border-radius: 8px;
      font-size: 14px;
    }
  }
  
  &__submit {
    border-radius: 8px;
    font-weight: 600;
    padding: 12px 24px;
    text-transform: none;
    
    &:hover:not(.disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
    }
    
    &.disabled {
      opacity: 0.6;
    }
  }
  
  &__links {
    text-align: center;
    margin-top: 20px;
  }
  
  &__link {
    color: #1976d2;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
    
    &:hover:not(.login-form__link--disabled) {
      color: #1565c0;
      text-decoration: underline;
    }
    
    &--disabled {
      color: #ccc;
      pointer-events: none;
    }
  }
}

// Focus styles for accessibility
.login-form__input {
  :deep(.q-field--focused) {
    .q-field__control {
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .login-form {
    &__input {
      :deep(.q-field__control) {
        border: 2px solid #000;
      }
      
      :deep(.q-field--focused .q-field__control) {
        border-color: #1976d2;
        box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.3);
      }
    }
    
    &__submit {
      border: 2px solid #1976d2;
    }
    
    &__link {
      text-decoration: underline;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .login-form {
    &__submit {
      transition: none;
      
      &:hover:not(.disabled) {
        transform: none;
      }
    }
    
    &__link {
      transition: none;
    }
  }
}

// Mobile optimizations
@media (max-width: 480px) {
  .login-form {
    &__field {
      margin-bottom: 16px;
    }
    
    &__input {
      :deep(.q-field__control) {
        min-height: 48px; // Larger touch targets
      }
    }
    
    &__submit {
      padding: 14px 24px;
      font-size: 16px; // Prevent zoom on iOS
    }
  }
}
</style>
