/**
 * Smart Factory WMS - Router Guards
 *
 * This file contains navigation guards for route protection,
 * authentication checks, and authorization validation.
 */

import store from "@/store";

/**
 * Authentication guard - requires user to be authenticated
 */
export const authGuard = async (to, from, next) => {
  // Check if user is authenticated
  if (store.getters["auth/isAuthenticated"]) {
    // Verify token is still valid
    try {
      const isValid = await store.dispatch("auth/checkAuth");
      if (isValid) {
        next();
        return;
      }
    } catch (error) {
      console.warn("Auth check failed:", error);
    }
  }

  // User is not authenticated or token is invalid
  // Store the intended route for redirect after login
  if (to.fullPath !== "/login") {
    store.commit("auth/SET_REDIRECT_ROUTE", to.fullPath);
  }
  next("/login");
};

/**
 * Guest guard - requires user to be NOT authenticated
 */
export const guestGuard = (to, from, next) => {
  if (!store.getters["auth/isAuthenticated"]) {
    next();
  } else {
    // Redirect authenticated users to dashboard
    next("/dashboard");
  }
};

/**
 * Role guard - requires user to have specific roles
 */
export const roleGuard = (requiredRoles) => {
  return (to, from, next) => {
    const userRoles = store.getters["auth/userRoles"];

    if (!userRoles || userRoles.length === 0) {
      next("/unauthorized");
      return;
    }

    const hasRequiredRole = requiredRoles.some((role) =>
      userRoles.includes(role)
    );

    if (hasRequiredRole) {
      next();
    } else {
      next("/unauthorized");
    }
  };
};

/**
 * Permission guard - requires user to have specific permissions
 */
export const permissionGuard = (requiredPermissions) => {
  return (to, from, next) => {
    const hasPermissions =
      store.getters["auth/hasPermissions"](requiredPermissions);

    if (hasPermissions) {
      next();
    } else {
      next("/unauthorized");
    }
  };
};

/**
 * Admin guard - requires user to be an administrator
 */
export const adminGuard = (to, from, next) => {
  const userRoles = store.getters["auth/userRoles"];

  if (userRoles && userRoles.includes("admin")) {
    next();
  } else {
    next("/unauthorized");
  }
};

/**
 * Manager guard - requires user to be a manager or admin
 */
export const managerGuard = (to, from, next) => {
  const userRoles = store.getters["auth/userRoles"];

  if (
    userRoles &&
    (userRoles.includes("admin") || userRoles.includes("manager"))
  ) {
    next();
  } else {
    next("/unauthorized");
  }
};

/**
 * Feature flag guard - requires feature to be enabled
 */
export const featureGuard = (featureName) => {
  return (to, from, next) => {
    const isFeatureEnabled =
      store.getters["settings/isFeatureEnabled"](featureName);

    if (isFeatureEnabled) {
      next();
    } else {
      next("/not-found");
    }
  };
};

/**
 * Maintenance mode guard - blocks access during maintenance
 */
export const maintenanceGuard = (to, from, next) => {
  const isMaintenanceMode = store.getters["settings/isMaintenanceMode"];

  if (isMaintenanceMode && to.name !== "Maintenance") {
    next("/maintenance");
  } else {
    next();
  }
};

/**
 * Rate limit guard - prevents too frequent navigation
 */
export const rateLimitGuard = (() => {
  const navigationHistory = new Map();
  const RATE_LIMIT_WINDOW = 1000; // 1 second
  const MAX_NAVIGATIONS = 5;

  return (to, from, next) => {
    const now = Date.now();
    const userId = store.getters["auth/userId"];
    const key = userId || "anonymous";

    if (!navigationHistory.has(key)) {
      navigationHistory.set(key, []);
    }

    const userHistory = navigationHistory.get(key);

    // Remove old entries
    const cutoff = now - RATE_LIMIT_WINDOW;
    while (userHistory.length > 0 && userHistory[0] < cutoff) {
      userHistory.shift();
    }

    // Check rate limit
    if (userHistory.length >= MAX_NAVIGATIONS) {
      console.warn("Navigation rate limit exceeded");
      // Don't block, just warn
      next();
      return;
    }

    // Add current navigation
    userHistory.push(now);
    next();
  };
})();

/**
 * Development guard - only allows access in development mode
 */
export const devGuard = (to, from, next) => {
  if (import.meta.env.DEV) {
    next();
  } else {
    next("/not-found");
  }
};

/**
 * Composite guard - combines multiple guards with AND logic
 */
export const combineGuards = (...guards) => {
  return async (to, from, next) => {
    for (const guard of guards) {
      try {
        await new Promise((resolve, reject) => {
          guard(to, from, (result) => {
            if (
              result === false ||
              typeof result === "string" ||
              typeof result === "object"
            ) {
              reject(result);
            } else {
              resolve();
            }
          });
        });
      } catch (result) {
        next(result);
        return;
      }
    }
    next();
  };
};

/**
 * Async guard wrapper - handles async guard functions
 */
export const asyncGuard = (asyncGuardFn) => {
  return async (to, from, next) => {
    try {
      const result = await asyncGuardFn(to, from);
      if (result === true || result === undefined) {
        next();
      } else {
        next(result);
      }
    } catch (error) {
      console.error("Async guard error:", error);
      next("/server-error");
    }
  };
};

/**
 * Data loading guard - ensures required data is loaded before navigation
 */
export const dataGuard = (dataLoader) => {
  return async (to, from, next) => {
    try {
      store.commit("ui/SET_LOADING", true);
      await dataLoader(to, from, store);
      next();
    } catch (error) {
      console.error("Data loading error:", error);
      next("/server-error");
    } finally {
      store.commit("ui/SET_LOADING", false);
    }
  };
};
