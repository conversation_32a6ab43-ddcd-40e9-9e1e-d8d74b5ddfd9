<template>
  <div class="auth-layout">
    <!-- Background -->
    <div class="auth-layout__background">
      <div class="auth-layout__background-overlay"></div>
    </div>
    
    <!-- Content -->
    <div class="auth-layout__content">
      <!-- Language Selector -->
      <div class="auth-layout__language-selector">
        <q-select
          v-model="currentLanguage"
          :options="languageOptions"
          :label="$t('auth.language_label')"
          outlined
          dense
          emit-value
          map-options
          options-dense
          class="auth-layout__language-select"
          @update:model-value="changeLanguage"
        >
          <template #prepend>
            <q-icon name="language" />
          </template>
        </q-select>
      </div>
      
      <!-- Main Card -->
      <div class="auth-layout__card-container">
        <q-card class="auth-layout__card" flat bordered>
          <!-- Logo and Title -->
          <q-card-section class="auth-layout__header">
            <div class="auth-layout__logo">
              <q-icon 
                name="factory" 
                size="48px" 
                color="primary"
                class="auth-layout__logo-icon"
              />
            </div>
            <h1 class="auth-layout__title">
              {{ $t('auth.title') }}
            </h1>
            <p class="auth-layout__subtitle">
              {{ $t('auth.welcome_message') }}
            </p>
          </q-card-section>
          
          <!-- Form Content -->
          <q-card-section class="auth-layout__form">
            <slot />
          </q-card-section>
        </q-card>
      </div>
      
      <!-- Footer -->
      <div class="auth-layout__footer">
        <p class="auth-layout__footer-text">
          © {{ currentYear }} Smart Factory WMS. All rights reserved.
        </p>
        <div class="auth-layout__footer-links">
          <a href="#" class="auth-layout__footer-link">Privacy Policy</a>
          <span class="auth-layout__footer-separator">•</span>
          <a href="#" class="auth-layout__footer-link">Terms of Service</a>
          <span class="auth-layout__footer-separator">•</span>
          <a href="#" class="auth-layout__footer-link">Support</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'

export default {
  name: 'AuthLayout',
  setup() {
    const { locale, t } = useI18n()
    const store = useStore()
    
    const currentLanguage = ref(locale.value)
    
    const languageOptions = [
      { label: 'English', value: 'en', flag: '🇺🇸' },
      { label: '日本語', value: 'ja', flag: '🇯🇵' },
      { label: '中文', value: 'zh', flag: '🇨🇳' },
      { label: 'Tiếng Việt', value: 'vi', flag: '🇻🇳' }
    ]
    
    const currentYear = computed(() => new Date().getFullYear())
    
    const changeLanguage = (newLanguage) => {
      locale.value = newLanguage
      currentLanguage.value = newLanguage
      
      // Save language preference
      store.commit('ui/SET_LANGUAGE', newLanguage)
      
      // Update document language
      document.documentElement.lang = newLanguage
    }
    
    onMounted(() => {
      // Set initial language from store if available
      const savedLanguage = store.state.ui?.language
      if (savedLanguage && savedLanguage !== currentLanguage.value) {
        changeLanguage(savedLanguage)
      }
    })
    
    return {
      currentLanguage,
      languageOptions,
      currentYear,
      changeLanguage
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  position: relative;
  
  &__background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -2;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      z-index: -1;
    }
    
    &-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      z-index: -1;
    }
  }
  
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    position: relative;
    z-index: 1;
  }
  
  &__language-selector {
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 10;
  }
  
  &__language-select {
    min-width: 150px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 8px;
  }
  
  &__card-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
  
  &__card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  &__header {
    text-align: center;
    padding: 32px 24px 24px;
  }
  
  &__logo {
    margin-bottom: 16px;
    
    &-icon {
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }
  }
  
  &__title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }
  
  &__subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
    line-height: 1.4;
  }
  
  &__form {
    padding: 0 24px 32px;
  }
  
  &__footer {
    margin-top: 32px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    
    &-text {
      font-size: 14px;
      margin: 0 0 8px 0;
    }
    
    &-links {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
    }
    
    &-link {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: color 0.2s ease;
      
      &:hover {
        color: white;
        text-decoration: underline;
      }
    }
    
    &-separator {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .auth-layout {
    &__content {
      padding: 16px;
      justify-content: flex-start;
      padding-top: 80px;
    }
    
    &__language-selector {
      top: 16px;
      right: 16px;
    }
    
    &__language-select {
      min-width: 120px;
    }
    
    &__card-container {
      max-width: 100%;
    }
    
    &__card {
      border-radius: 12px;
    }
    
    &__header {
      padding: 24px 20px 20px;
    }
    
    &__title {
      font-size: 24px;
    }
    
    &__subtitle {
      font-size: 14px;
    }
    
    &__form {
      padding: 0 20px 24px;
    }
    
    &__footer {
      margin-top: 24px;
      
      &-links {
        flex-direction: column;
        gap: 4px;
      }
      
      &-separator {
        display: none;
      }
    }
  }
}

@media (max-width: 480px) {
  .auth-layout {
    &__content {
      padding: 12px;
      padding-top: 70px;
    }
    
    &__language-selector {
      top: 12px;
      right: 12px;
    }
    
    &__header {
      padding: 20px 16px 16px;
    }
    
    &__title {
      font-size: 22px;
    }
    
    &__form {
      padding: 0 16px 20px;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .auth-layout {
    &__card {
      background: white;
      border: 2px solid #000;
    }
    
    &__title {
      color: #000;
    }
    
    &__subtitle {
      color: #333;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .auth-layout {
    &__footer-link {
      transition: none;
    }
  }
}
</style>
