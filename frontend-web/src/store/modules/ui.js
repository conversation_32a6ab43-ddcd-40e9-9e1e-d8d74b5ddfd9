/**
 * Smart Factory WMS - UI Store Module
 * 
 * This module manages UI state including theme, language,
 * loading states, and user interface preferences.
 */

const state = {
  // Loading states
  loading: false,
  loadingMessage: '',
  
  // Theme and appearance
  theme: 'light',
  darkMode: false,
  
  // Language and localization
  language: 'en',
  
  // Layout preferences
  sidebarCollapsed: false,
  sidebarMini: false,
  
  // Notifications
  notifications: [],
  
  // Modal and dialog states
  activeModals: [],
  
  // Page states
  pageTitle: '',
  breadcrumbs: [],
  
  // Mobile responsiveness
  isMobile: false,
  screenSize: 'desktop', // mobile, tablet, desktop
  
  // Feature flags
  features: {
    darkMode: true,
    notifications: true,
    breadcrumbs: true,
    sidebar: true
  }
}

const getters = {
  // Loading states
  isLoading: (state) => state.loading,
  loadingMessage: (state) => state.loadingMessage,
  
  // Theme
  theme: (state) => state.theme,
  isDarkMode: (state) => state.darkMode,
  
  // Language
  language: (state) => state.language,
  
  // Layout
  isSidebarCollapsed: (state) => state.sidebarCollapsed,
  isSidebarMini: (state) => state.sidebarMini,
  
  // Notifications
  notifications: (state) => state.notifications,
  unreadNotifications: (state) => state.notifications.filter(n => !n.read),
  notificationCount: (state, getters) => getters.unreadNotifications.length,
  
  // Modals
  activeModals: (state) => state.activeModals,
  hasActiveModal: (state) => state.activeModals.length > 0,
  
  // Page
  pageTitle: (state) => state.pageTitle,
  breadcrumbs: (state) => state.breadcrumbs,
  
  // Responsive
  isMobile: (state) => state.isMobile,
  screenSize: (state) => state.screenSize,
  
  // Features
  isFeatureEnabled: (state) => (featureName) => {
    return state.features[featureName] || false
  }
}

const mutations = {
  // Loading states
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_LOADING_MESSAGE(state, message) {
    state.loadingMessage = message
  },
  
  // Theme
  SET_THEME(state, theme) {
    state.theme = theme
    state.darkMode = theme === 'dark'
  },
  
  SET_DARK_MODE(state, darkMode) {
    state.darkMode = darkMode
    state.theme = darkMode ? 'dark' : 'light'
  },
  
  // Language
  SET_LANGUAGE(state, language) {
    state.language = language
  },
  
  // Layout
  SET_SIDEBAR_COLLAPSED(state, collapsed) {
    state.sidebarCollapsed = collapsed
  },
  
  SET_SIDEBAR_MINI(state, mini) {
    state.sidebarMini = mini
  },
  
  TOGGLE_SIDEBAR() {
    state.sidebarCollapsed = !state.sidebarCollapsed
  },
  
  // Notifications
  ADD_NOTIFICATION(state, notification) {
    const id = Date.now() + Math.random()
    state.notifications.unshift({
      id,
      read: false,
      timestamp: new Date(),
      ...notification
    })
  },
  
  REMOVE_NOTIFICATION(state, notificationId) {
    const index = state.notifications.findIndex(n => n.id === notificationId)
    if (index > -1) {
      state.notifications.splice(index, 1)
    }
  },
  
  MARK_NOTIFICATION_READ(state, notificationId) {
    const notification = state.notifications.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  },
  
  MARK_ALL_NOTIFICATIONS_READ(state) {
    state.notifications.forEach(n => {
      n.read = true
    })
  },
  
  CLEAR_NOTIFICATIONS(state) {
    state.notifications = []
  },
  
  // Modals
  OPEN_MODAL(state, modalId) {
    if (!state.activeModals.includes(modalId)) {
      state.activeModals.push(modalId)
    }
  },
  
  CLOSE_MODAL(state, modalId) {
    const index = state.activeModals.indexOf(modalId)
    if (index > -1) {
      state.activeModals.splice(index, 1)
    }
  },
  
  CLOSE_ALL_MODALS(state) {
    state.activeModals = []
  },
  
  // Page
  SET_PAGE_TITLE(state, title) {
    state.pageTitle = title
    if (typeof document !== 'undefined') {
      document.title = title ? `${title} - Smart Factory WMS` : 'Smart Factory WMS'
    }
  },
  
  SET_BREADCRUMBS(state, breadcrumbs) {
    state.breadcrumbs = breadcrumbs
  },
  
  ADD_BREADCRUMB(state, breadcrumb) {
    state.breadcrumbs.push(breadcrumb)
  },
  
  CLEAR_BREADCRUMBS(state) {
    state.breadcrumbs = []
  },
  
  // Responsive
  SET_MOBILE(state, isMobile) {
    state.isMobile = isMobile
  },
  
  SET_SCREEN_SIZE(state, screenSize) {
    state.screenSize = screenSize
    state.isMobile = screenSize === 'mobile'
  },
  
  // Features
  SET_FEATURE(state, { featureName, enabled }) {
    state.features[featureName] = enabled
  }
}

const actions = {
  // Loading actions
  setLoading({ commit }, loading) {
    commit('SET_LOADING', loading)
  },
  
  setLoadingWithMessage({ commit }, { loading, message }) {
    commit('SET_LOADING', loading)
    commit('SET_LOADING_MESSAGE', message)
  },
  
  // Theme actions
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme)
    
    // Apply theme to document
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', theme)
      document.body.classList.toggle('dark', theme === 'dark')
    }
  },
  
  toggleDarkMode({ commit, state }) {
    const newMode = !state.darkMode
    commit('SET_DARK_MODE', newMode)
    
    // Apply theme to document
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', newMode ? 'dark' : 'light')
      document.body.classList.toggle('dark', newMode)
    }
  },
  
  // Language actions
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
    
    // Update document language
    if (typeof document !== 'undefined') {
      document.documentElement.lang = language
    }
  },
  
  // Notification actions
  showNotification({ commit }, notification) {
    commit('ADD_NOTIFICATION', notification)
    
    // Auto-remove after timeout if specified
    if (notification.timeout) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', notification.id)
      }, notification.timeout)
    }
  },
  
  showSuccess({ dispatch }, message) {
    dispatch('showNotification', {
      type: 'success',
      title: 'Success',
      message,
      timeout: 3000
    })
  },
  
  showError({ dispatch }, message) {
    dispatch('showNotification', {
      type: 'error',
      title: 'Error',
      message,
      timeout: 6000
    })
  },
  
  showWarning({ dispatch }, message) {
    dispatch('showNotification', {
      type: 'warning',
      title: 'Warning',
      message,
      timeout: 5000
    })
  },
  
  showInfo({ dispatch }, message) {
    dispatch('showNotification', {
      type: 'info',
      title: 'Information',
      message,
      timeout: 4000
    })
  },
  
  // Page actions
  setPageTitle({ commit }, title) {
    commit('SET_PAGE_TITLE', title)
  },
  
  setBreadcrumbs({ commit }, breadcrumbs) {
    commit('SET_BREADCRUMBS', breadcrumbs)
  },
  
  // Responsive actions
  updateScreenSize({ commit }) {
    if (typeof window === 'undefined') return
    
    const width = window.innerWidth
    let screenSize = 'desktop'
    
    if (width < 768) {
      screenSize = 'mobile'
    } else if (width < 1024) {
      screenSize = 'tablet'
    }
    
    commit('SET_SCREEN_SIZE', screenSize)
  },
  
  // Initialize UI
  initializeUI({ dispatch, commit }) {
    // Set up responsive listener
    if (typeof window !== 'undefined') {
      dispatch('updateScreenSize')
      
      window.addEventListener('resize', () => {
        dispatch('updateScreenSize')
      })
    }
    
    // Initialize theme
    const savedTheme = localStorage.getItem('ui_preferences')
    if (savedTheme) {
      try {
        const preferences = JSON.parse(savedTheme)
        if (preferences.theme) {
          dispatch('setTheme', preferences.theme)
        }
        if (preferences.language) {
          dispatch('setLanguage', preferences.language)
        }
      } catch (error) {
        console.warn('Failed to load UI preferences:', error)
      }
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
