# User Management & Authentication - Data Design

## 1. Entity-Relationship Diagrams

### Core User Management ERD

```mermaid
erDiagram
    USERS }o--o{ ROLES : assigned
    ROLES }o--o{ PERMISSIONS : grants

    USERS ||--o{ USER_SESSIONS : creates
    USERS ||--o{ USER_AUDIT_LOGS : generates
    USERS ||--o{ PASSWORD_RESET_TOKENS : requests

    ROLES ||--o{ ROLE_PERMISSIONS : contains

    USERS {
        int user_id PK
        string username UK
        string email UK
        string password_hash
        string first_name
        string last_name
        string phone_number
        string preferred_language
        boolean is_active
        boolean email_verified
        boolean must_change_password
        timestamp last_login
        timestamp password_changed_at
        int failed_login_attempts
        timestamp locked_until
        timestamp created_at
        timestamp updated_at
    }

    ROLES {
        int role_id PK
        string role_name UK
        string description
        boolean is_system_role
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    PERMISSIONS {
        int permission_id PK
        string permission_name UK
        string resource
        string action
        string description
        timestamp created_at
    }

    USER_ROLES {
        int user_id FK
        int role_id FK
        timestamp assigned_at
        int assigned_by FK
    }

    ROLE_PERMISSIONS {
        int role_id FK
        int permission_id FK
        timestamp granted_at
        int granted_by FK
    }

    USER_SESSIONS {
        string session_id PK
        int user_id FK
        string refresh_token_hash
        string device_info
        string ip_address
        string user_agent
        timestamp expires_at
        boolean is_active
        timestamp created_at
        timestamp last_accessed
    }

    USER_AUDIT_LOGS {
        bigint log_id PK
        int user_id FK
        string action
        string resource
        text details
        string ip_address
        string user_agent
        boolean success
        timestamp created_at
    }

    PASSWORD_RESET_TOKENS {
        string token_id PK
        int user_id FK
        string token_hash
        timestamp expires_at
        boolean is_used
        timestamp created_at
        timestamp used_at
    }
```

## 2. Schema Definitions

### Core Tables

#### users
```sql
CREATE TABLE public.users (
    user_id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    phone_number VARCHAR(20),
    preferred_language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    must_change_password BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT users_username_check CHECK (LENGTH(username) >= 3 AND username ~ '^[a-zA-Z0-9_.-]+$'),
    CONSTRAINT users_email_check CHECK (email ~ '^[^@]+@[^@]+\.[^@]+$'),
    CONSTRAINT users_password_hash_check CHECK (LENGTH(password_hash) >= 60),
    CONSTRAINT users_failed_attempts_check CHECK (failed_login_attempts >= 0 AND failed_login_attempts <= 10),
    CONSTRAINT users_language_check CHECK (preferred_language IN ('en', 'ja', 'zh', 'vi'))
);

-- Indexes
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_username ON public.users(username);
CREATE INDEX idx_users_is_active ON public.users(is_active);
CREATE INDEX idx_users_last_login ON public.users(last_login);
CREATE INDEX idx_users_locked_until ON public.users(locked_until) WHERE locked_until IS NOT NULL;

-- Triggers
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### roles
```sql
CREATE TABLE public.roles (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT roles_name_check CHECK (LENGTH(role_name) >= 2 AND role_name ~ '^[a-zA-Z0-9_\s-]+$')
);

-- Indexes
CREATE INDEX idx_roles_is_active ON public.roles(is_active);
CREATE INDEX idx_roles_is_system ON public.roles(is_system_role);

-- Triggers
CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON public.roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### permissions
```sql
CREATE TABLE public.permissions (
    permission_id SERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE (resource, action),

    CONSTRAINT permissions_name_check CHECK (LENGTH(permission_name) >= 3),
    CONSTRAINT permissions_resource_check CHECK (resource ~ '^[a-z][a-z0-9_]*$'),
    CONSTRAINT permissions_action_check CHECK (action IN ('create', 'read', 'update', 'delete', 'execute', 'manage'))
);

-- Indexes
CREATE INDEX idx_permissions_resource_action ON public.permissions(resource, action);
```

#### user_roles
```sql
CREATE TABLE public.user_roles (
    user_id INTEGER NOT NULL REFERENCES public.users(user_id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES public.roles(role_id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by INTEGER REFERENCES public.users(user_id),

    PRIMARY KEY (user_id, role_id)
);

-- Indexes
CREATE INDEX idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON public.user_roles(role_id);
CREATE INDEX idx_user_roles_assigned_by ON public.user_roles(assigned_by);
```

#### role_permissions
```sql
CREATE TABLE public.role_permissions (
    role_id INTEGER NOT NULL REFERENCES public.roles(role_id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES public.permissions(permission_id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by INTEGER REFERENCES public.users(user_id),

    PRIMARY KEY (role_id, permission_id)
);

-- Indexes
CREATE INDEX idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON public.role_permissions(permission_id);
CREATE INDEX idx_role_permissions_granted_by ON public.role_permissions(granted_by);
```

### Session and Security Tables

#### user_sessions
```sql
CREATE TABLE public.user_sessions (
    session_id VARCHAR(128) PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(user_id) ON DELETE CASCADE,
    refresh_token_hash VARCHAR(255) NOT NULL,
    device_info VARCHAR(200),
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT sessions_token_hash_check CHECK (LENGTH(refresh_token_hash) >= 60),
    CONSTRAINT sessions_expires_check CHECK (expires_at > created_at)
);

-- Indexes
CREATE INDEX idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON public.user_sessions(expires_at);
CREATE INDEX idx_user_sessions_is_active ON public.user_sessions(is_active);
CREATE INDEX idx_user_sessions_refresh_token ON public.user_sessions(refresh_token_hash);

-- Triggers
CREATE TRIGGER update_user_sessions_last_accessed
    BEFORE UPDATE ON public.user_sessions
    FOR EACH ROW EXECUTE FUNCTION update_last_accessed_column();
```

#### user_audit_logs
```sql
CREATE TABLE public.user_audit_logs (
    log_id BIGSERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES public.users(user_id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(50),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT audit_logs_action_check CHECK (LENGTH(action) >= 1)
);

-- Indexes
CREATE INDEX idx_user_audit_logs_user_id ON public.user_audit_logs(user_id);
CREATE INDEX idx_user_audit_logs_action ON public.user_audit_logs(action);
CREATE INDEX idx_user_audit_logs_created_at ON public.user_audit_logs(created_at);
CREATE INDEX idx_user_audit_logs_success ON public.user_audit_logs(success);
CREATE INDEX idx_user_audit_logs_details ON public.user_audit_logs USING GIN(details);

-- Partitioning by month for performance
CREATE TABLE public.user_audit_logs_y2024m01 PARTITION OF public.user_audit_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

#### password_reset_tokens
```sql
CREATE TABLE public.password_reset_tokens (
    token_id VARCHAR(128) PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT reset_tokens_hash_check CHECK (LENGTH(token_hash) >= 60),
    CONSTRAINT reset_tokens_expires_check CHECK (expires_at > created_at),
    CONSTRAINT reset_tokens_used_check CHECK (
        (is_used = FALSE AND used_at IS NULL) OR
        (is_used = TRUE AND used_at IS NOT NULL)
    )
);

-- Indexes
CREATE INDEX idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
CREATE INDEX idx_password_reset_tokens_expires_at ON public.password_reset_tokens(expires_at);
CREATE INDEX idx_password_reset_tokens_is_used ON public.password_reset_tokens(is_used);
CREATE INDEX idx_password_reset_tokens_token_hash ON public.password_reset_tokens(token_hash);
```

## 3. Data Dictionary

### Business Terms and Definitions

| Term | Definition | Business Rules |
|------|------------|----------------|
| **User** | An individual with access to the system | Must have unique username and email |
| **Role** | A collection of permissions defining what a user can do | Can be assigned to multiple users |
| **Permission** | A specific action allowed on a resource | Defined as resource + action combination |
| **Session** | An authenticated user's active connection to the system | Has expiration time and can be revoked |
| **Audit Log** | A record of user actions for security and compliance | Immutable once created |

### Field Descriptions and Constraints

#### Users Table Fields

| Field | Data Type | Description | Constraints | Valid Values |
|-------|-----------|-------------|-------------|--------------|
| `user_id` | SERIAL | Unique identifier for user | Primary Key, Auto-increment | Positive integers |
| `username` | VARCHAR(50) | Unique username | 3-50 chars, alphanumeric + _.- | a-zA-Z0-9_.- |
| `email` | VARCHAR(100) | User's email address | Valid email format | RFC 5322 compliant |
| `password_hash` | VARCHAR(255) | Bcrypt hashed password | Min 60 chars (bcrypt output) | Bcrypt hash format |
| `first_name` | VARCHAR(50) | User's first name | Optional, 1-50 chars | Unicode letters |
| `last_name` | VARCHAR(50) | User's last name | Optional, 1-50 chars | Unicode letters |
| `phone_number` | VARCHAR(20) | User's phone number | Optional, international format | E.164 format preferred |
| `preferred_language` | VARCHAR(10) | UI language preference | Default 'en' | en, ja, zh, vi |
| `is_active` | BOOLEAN | Whether user account is active | Default TRUE | TRUE, FALSE |
| `email_verified` | BOOLEAN | Whether email is verified | Default FALSE | TRUE, FALSE |
| `must_change_password` | BOOLEAN | Force password change on next login | Default FALSE | TRUE, FALSE |
| `failed_login_attempts` | INTEGER | Count of consecutive failed logins | 0-10, resets on success | 0-10 |
| `locked_until` | TIMESTAMP | Account lockout expiration | NULL if not locked | Future timestamp |

#### Roles Table Fields

| Field | Data Type | Description | Constraints | Valid Values |
|-------|-----------|-------------|-------------|--------------|
| `role_id` | SERIAL | Unique identifier for role | Primary Key, Auto-increment | Positive integers |
| `role_name` | VARCHAR(50) | Unique role name | 2-50 chars, alphanumeric + spaces | a-zA-Z0-9_\s- |
| `description` | TEXT | Role description | Optional | Any text |
| `is_system_role` | BOOLEAN | Whether role is system-defined | Default FALSE | TRUE, FALSE |
| `is_active` | BOOLEAN | Whether role is active | Default TRUE | TRUE, FALSE |

#### Permissions Table Fields

| Field | Data Type | Description | Constraints | Valid Values |
|-------|-----------|-------------|-------------|--------------|
| `permission_id` | SERIAL | Unique identifier for permission | Primary Key, Auto-increment | Positive integers |
| `permission_name` | VARCHAR(100) | Human-readable permission name | 3-100 chars, unique | Descriptive text |
| `resource` | VARCHAR(50) | Resource being protected | Lowercase, underscore separated | users, roles, inventory, etc. |
| `action` | VARCHAR(20) | Action allowed on resource | Predefined values | create, read, update, delete, execute, manage |
| `description` | TEXT | Permission description | Optional | Any text |

### Data Validation Rules

#### Password Policy
- **Minimum Length**: 8 characters
- **Complexity**: Must contain at least 3 of the following:
  - Uppercase letters (A-Z)
  - Lowercase letters (a-z)
  - Numbers (0-9)
  - Special characters (!@#$%^&*)
- **History**: Cannot reuse last 5 passwords
- **Expiration**: Passwords expire after 90 days (configurable)

#### Account Lockout Policy
- **Failed Attempts Threshold**: 5 consecutive failed login attempts
- **Lockout Duration**: 15 minutes (configurable)
- **Progressive Lockout**: Increases with repeated lockouts
- **Admin Override**: Administrators can unlock accounts manually

#### Session Management Rules
- **Access Token Lifetime**: 30 minutes (configurable)
- **Refresh Token Lifetime**: 7 days (configurable)
- **Concurrent Sessions**: Maximum 5 active sessions per user
- **Idle Timeout**: 2 hours of inactivity
- **Device Tracking**: Track device information for security

## 4. Database Constraints and Indexes

### Primary Key Constraints
```sql
-- All tables have appropriate primary keys defined
ALTER TABLE public.users ADD CONSTRAINT pk_users PRIMARY KEY (user_id);
ALTER TABLE public.roles ADD CONSTRAINT pk_roles PRIMARY KEY (role_id);
ALTER TABLE public.permissions ADD CONSTRAINT pk_permissions PRIMARY KEY (permission_id);
```

### Foreign Key Constraints
```sql
-- User-Role relationships
ALTER TABLE public.user_roles
    ADD CONSTRAINT fk_user_roles_user
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;

ALTER TABLE public.user_roles
    ADD CONSTRAINT fk_user_roles_role
    FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;

-- Role-Permission relationships
ALTER TABLE public.role_permissions
    ADD CONSTRAINT fk_role_permissions_role
    FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;

ALTER TABLE public.role_permissions
    ADD CONSTRAINT fk_role_permissions_permission
    FOREIGN KEY (permission_id) REFERENCES public.permissions(permission_id) ON DELETE CASCADE;
```

### Unique Constraints
```sql
-- User uniqueness
ALTER TABLE public.users ADD CONSTRAINT uk_users_username UNIQUE (username);
ALTER TABLE public.users ADD CONSTRAINT uk_users_email UNIQUE (email);

-- Role uniqueness
ALTER TABLE public.roles ADD CONSTRAINT uk_roles_name UNIQUE (role_name);

-- Permission uniqueness
ALTER TABLE public.permissions ADD CONSTRAINT uk_permissions_name UNIQUE (permission_name);
ALTER TABLE public.permissions ADD CONSTRAINT uk_permissions_resource_action UNIQUE (resource, action);
```

### Performance Indexes
```sql
-- User queries
CREATE INDEX idx_users_active ON public.users(is_active);
CREATE INDEX idx_roles_active ON public.roles(is_active);

-- Authentication queries
CREATE INDEX idx_users_login ON public.users(username, is_active);
CREATE INDEX idx_users_email_login ON public.users(email, is_active);

-- Session management
CREATE INDEX idx_sessions_user_active ON public.user_sessions(user_id, is_active);
CREATE INDEX idx_sessions_cleanup ON public.user_sessions(expires_at, is_active);

-- Audit log queries
CREATE INDEX idx_audit_user_date ON public.user_audit_logs(user_id, created_at);

-- Permission resolution
CREATE INDEX idx_user_roles_lookup ON public.user_roles(user_id, role_id);
CREATE INDEX idx_role_permissions_lookup ON public.role_permissions(role_id, permission_id);
```

## 5. Data Archival and Retention

### Audit Log Retention
- **Active Period**: 2 years in main table
- **Archive Period**: 5 years in archive storage
- **Purge Policy**: Automatic deletion after 7 years
- **Partitioning**: Monthly partitions for performance

### Session Cleanup
- **Expired Sessions**: Automatic cleanup daily
- **Inactive Sessions**: Cleanup after 30 days
- **Batch Processing**: Off-peak hours cleanup

### Password Reset Tokens
- **Expiration**: 24 hours from creation
- **Cleanup**: Daily removal of expired tokens
- **Usage Tracking**: Track token usage for security

This data design ensures robust user management with proper security, performance, and maintainability considerations for the Smart Factory WMS system.
```
```