{"task_name": "Implement Password Reset Flow for Smart Factory WMS", "description": "Create a secure multi-step password reset flow including request, email verification, and password confirmation screens.", "requirements": {"functional_requirements": ["Password reset request with email validation", "Email verification and token validation", "New password creation with strength requirements", "Password confirmation and matching validation", "Success confirmation and redirect to login", "Error handling for expired/invalid tokens", "Rate limiting and security measures", "Multi-language support for all steps"], "technical_requirements": ["Vue.js 3 with Composition API and TypeScript", "Multi-step form with progress indication", "Real-time password strength validation", "Secure token handling and validation", "Accessibility compliance (WCAG 2.1 AA)", "Responsive design for all devices", "Integration with authentication APIs", "Client-side security best practices"], "flow_steps": {"step_1": {"name": "Request Reset", "description": "User enters email address to request password reset", "components": ["Email input field with validation", "Send reset link button", "Back to login link", "Loading state during request", "Success/error message display"]}, "step_2": {"name": "<PERSON>ail Sent Confirmation", "description": "Confirmation that reset email has been sent", "components": ["Success message with instructions", "Resend email option (with cooldown)", "Back to login link", "Email not received help text"]}, "step_3": {"name": "Reset Password", "description": "User creates new password using token from email", "components": ["New password input with strength indicator", "Confirm password input", "Password requirements checklist", "Update password button", "Token validation and error handling"]}, "step_4": {"name": "Success Confirmation", "description": "Password successfully updated confirmation", "components": ["Success message", "Automatic redirect to login", "Manual login link", "Security recommendations"]}}}, "api_integration": {"endpoints": {"request_reset": {"url": "POST /auth/v1/password-reset/request", "request_body": {"email": "string (valid email format)"}, "response": {"success": {"message": "Reset email sent", "email_sent": true}, "error": {"error_code": "string", "message": "string"}}}, "validate_token": {"url": "GET /auth/v1/password-reset/validate/{token}", "response": {"valid": "boolean", "expires_at": "datetime", "email": "string (masked)"}}, "confirm_reset": {"url": "POST /auth/v1/password-reset/confirm", "request_body": {"token": "string", "new_password": "string"}, "response": {"success": "boolean", "message": "string"}}}, "error_codes": {"EMAIL_NOT_FOUND": "Email address not found in system", "TOKEN_EXPIRED": "Reset token has expired", "TOKEN_INVALID": "Reset token is invalid or already used", "RATE_LIMITED": "Too many reset requests", "PASSWORD_WEAK": "Password does not meet requirements", "SERVER_ERROR": "Internal server error"}}, "password_requirements": {"minimum_length": 8, "maximum_length": 128, "required_characters": ["At least one uppercase letter (A-Z)", "At least one lowercase letter (a-z)", "At least one number (0-9)", "At least one special character (!@#$%^&*)"], "forbidden_patterns": ["Common passwords (password123, etc.)", "Sequential characters (123456, abcdef)", "Repeated characters (aaaaaa, 111111)", "User's email or username"], "strength_indicator": {"weak": "Red - Does not meet minimum requirements", "fair": "Orange - Meets basic requirements", "good": "Yellow - Good password strength", "strong": "Green - Strong password"}}, "security_measures": {"rate_limiting": {"reset_requests": "Maximum 3 requests per email per hour", "token_validation": "Maximum 10 attempts per token", "password_attempts": "Maximum 5 password submission attempts"}, "token_security": {"expiration": "15 minutes from generation", "single_use": "<PERSON><PERSON> invalidated after successful use", "secure_generation": "Cryptographically secure random tokens", "length": "32 characters minimum"}, "client_security": ["No token storage in localStorage", "Secure token transmission", "Input sanitization", "CSRF protection", "No password logging"]}, "user_experience": {"progress_indication": {"step_indicator": "Visual progress bar showing current step", "step_labels": "Clear labels for each step", "completion_percentage": "Progress percentage display"}, "loading_states": {"email_sending": "Loading spinner during email request", "token_validation": "Loading state while validating token", "password_update": "Loading state during password update", "auto_redirect": "Countdown timer for automatic redirect"}, "error_recovery": ["Clear error messages with recovery suggestions", "Retry mechanisms for failed operations", "Alternative contact methods for help", "Graceful handling of expired sessions"]}, "form_validation": {"email_validation": {"format": "Valid email format (RFC 5322)", "real_time": "Immediate validation on blur", "server_side": "Async validation against user database"}, "password_validation": {"real_time": "Live validation as user types", "strength_meter": "Visual strength indicator", "requirements_checklist": "Dynamic checklist showing met/unmet requirements", "confirmation_match": "Real-time matching validation"}, "token_validation": {"format": "Valid token format", "expiration": "Check token expiration", "usage": "Verify token hasn't been used"}}, "accessibility_features": ["Keyboard navigation between steps", "Screen reader announcements for step changes", "ARIA labels for all form controls", "Focus management for dynamic content", "High contrast mode support", "Clear instructions and help text", "Error announcements for screen readers", "Skip links for navigation"], "internationalization": {"text_keys": {"step1_title": "Reset Your Password", "step1_description": "Enter your email address and we'll send you a link to reset your password", "email_label": "Email Address", "send_reset_link": "Send Reset Link", "back_to_login": "Back to Sign In", "step2_title": "Check Your Email", "step2_description": "We've sent a password reset link to {email}", "step2_instructions": "Click the link in the email to reset your password", "resend_email": "<PERSON><PERSON><PERSON>", "email_not_received": "Didn't receive the email? Check your spam folder or contact support", "step3_title": "Create New Password", "step3_description": "Enter your new password below", "new_password_label": "New Password", "confirm_password_label": "Confirm Password", "password_requirements": "Password Requirements:", "requirement_length": "At least 8 characters", "requirement_uppercase": "At least one uppercase letter", "requirement_lowercase": "At least one lowercase letter", "requirement_number": "At least one number", "requirement_special": "At least one special character", "update_password": "Update Password", "step4_title": "Password Updated Successfully", "step4_description": "Your password has been updated. You can now sign in with your new password", "redirecting": "Redirecting to login in {seconds} seconds...", "go_to_login": "Go to Sign In", "error_email_not_found": "Email address not found", "error_token_expired": "Reset link has expired. Please request a new one", "error_token_invalid": "Invalid reset link. Please request a new one", "error_rate_limited": "Too many reset requests. Please try again later", "error_password_weak": "Password does not meet security requirements", "error_passwords_mismatch": "Passwords do not match", "error_network": "Network error. Please try again", "error_server": "Server error. Please try again later"}}, "state_management": {"flow_state": {"current_step": "integer (1-4)", "email": "string (user's email)", "token": "string (reset token from URL)", "token_valid": "boolean", "token_expires_at": "datetime", "loading": "boolean", "error": "error object", "success": "boolean"}, "form_state": {"email_form": {"email": "string", "is_valid": "boolean", "errors": "array"}, "password_form": {"new_password": "string", "confirm_password": "string", "password_strength": "string", "requirements_met": "object", "is_valid": "boolean", "errors": "array"}}}, "routing": {"routes": ["/password-reset - Step 1: Request reset", "/password-reset/sent - Step 2: <PERSON><PERSON> sent confirmation", "/password-reset/confirm/:token - Step 3: Set new password", "/password-reset/success - Step 4: Success confirmation"], "navigation": {"forward": "Automatic progression through steps", "backward": "Limited backward navigation for security", "direct_access": "Token-based access to step 3", "guards": "Route guards for step validation"}}, "testing_requirements": {"unit_tests": ["Password strength validation", "Email format validation", "Token validation logic", "Form state management", "Error handling functions"], "integration_tests": ["Complete password reset flow", "API integration scenarios", "Error handling flows", "Token expiration handling", "Rate limiting behavior"], "e2e_tests": ["Full password reset journey", "Email link navigation", "Error scenarios testing", "Accessibility compliance", "Multi-language testing"]}, "implementation_notes": ["Use Vue 3 Composition API with TypeScript", "Implement step-by-step navigation with Vue Router", "Use VeeValidate for form validation", "Implement password strength meter with zxcvbn", "Use Pinia for state management", "Follow security best practices for password handling", "Implement proper error boundaries", "Use Vue I18n for internationalization", "Optimize for Core Web Vitals"], "deliverables": ["PasswordResetFlow.vue main component", "RequestResetStep.vue component", "EmailSentStep.vue component", "ConfirmResetStep.vue component", "SuccessStep.vue component", "PasswordStrengthMeter.vue component", "Password reset composables", "Validation schemas", "Type definitions", "Unit and integration tests", "Documentation"]}