{"task_name": "Implement Mobile Authentication Screens for Smart Factory WMS", "description": "Create mobile-optimized authentication screens using Flutter for the Smart Factory WMS mobile application with native platform integration.", "requirements": {"functional_requirements": ["Mobile login screen with touch-optimized interface", "Biometric authentication (fingerprint, face ID)", "Remember device functionality", "Offline authentication capability", "Push notification integration", "Auto-logout on app backgrounding", "Secure token storage in device keychain", "Multi-language support for mobile platforms"], "technical_requirements": ["Flutter 3.x with <PERSON><PERSON>", "Platform-specific UI adaptations (iOS/Android)", "Secure storage using flutter_secure_storage", "Biometric authentication with local_auth", "HTTP client with dio for API integration", "State management with Riverpod or Bloc", "Responsive design for various screen sizes", "Accessibility support for mobile platforms"], "platform_specifications": {"ios": {"design_language": "iOS Human Interface Guidelines", "authentication": "Face ID, Touch ID support", "keychain": "iOS Keychain Services integration", "notifications": "APNs integration", "accessibility": "VoiceOver support"}, "android": {"design_language": "Material Design 3", "authentication": "Fingerprint, Face unlock support", "keychain": "Android Keystore integration", "notifications": "FCM integration", "accessibility": "TalkBack support"}}}, "screen_specifications": {"login_screen": {"layout": "Single column with large touch targets", "components": ["App logo and branding", "Username/email input field", "Password input field with visibility toggle", "Biometric login button (if available)", "Remember device toggle", "Sign in button", "Forgot password link", "Language selector", "Loading indicator"], "touch_targets": "Minimum 44dp/pt for all interactive elements", "keyboard_handling": "Automatic keyboard avoidance and input focus"}, "biometric_prompt": {"native_integration": "Platform-specific biometric prompts", "fallback": "PIN/pattern fallback for biometric failure", "error_handling": "Clear error messages for biometric failures", "security": "Secure enclave/TEE integration"}, "offline_mode": {"cached_credentials": "Secure local credential verification", "sync_indicator": "Clear indication of offline status", "data_sync": "Background sync when connection restored", "limitations": "Clear communication of offline limitations"}}, "api_integration": {"authentication_flow": {"login_endpoint": "POST /auth/v1/mobile/login", "token_refresh": "POST /auth/v1/mobile/refresh", "device_registration": "POST /auth/v1/mobile/devices", "biometric_setup": "POST /auth/v1/mobile/biometric"}, "request_structure": {"login": {"username": "string", "password": "string", "device_id": "string (unique device identifier)", "device_info": {"platform": "string (ios/android)", "version": "string", "model": "string", "app_version": "string"}, "biometric_enabled": "boolean"}}, "response_handling": {"success": "Store tokens securely, setup biometric if enabled", "failure": "Clear error messages with retry options", "network_error": "Offline mode activation if credentials cached"}}, "security_implementation": {"token_storage": {"access_token": "Secure storage with biometric protection", "refresh_token": "Encrypted storage in device keychain", "expiration_handling": "Automatic token refresh with fallback"}, "biometric_security": {"enrollment_check": "Verify biometric enrollment before enabling", "secure_enclave": "Use hardware security module when available", "fallback_auth": "PIN/password fallback for biometric failures", "invalidation": "Handle biometric changes (new fingerprint added)"}, "device_security": {"jailbreak_detection": "Detect rooted/jailbroken devices", "certificate_pinning": "SSL certificate pinning for API calls", "app_integrity": "Runtime application self-protection", "screen_recording": "Prevent screen recording of sensitive screens"}}, "offline_capabilities": {"credential_caching": {"encrypted_storage": "Securely cache user credentials", "expiration": "Cached credentials expire after 7 days", "validation": "Local validation against cached hash", "sync_required": "Force online sync after offline period"}, "data_synchronization": {"background_sync": "Sync when app returns online", "conflict_resolution": "Handle data conflicts gracefully", "progress_indication": "Show sync progress to user", "failure_handling": "Retry mechanism for failed syncs"}}, "user_experience": {"onboarding": {"first_launch": "Introduction to app features", "biometric_setup": "Optional biometric authentication setup", "permissions": "Request necessary permissions with explanations", "tutorial": "Quick tutorial for key features"}, "authentication_flow": {"quick_login": "Biometric login as primary option", "fallback_options": "Clear fallback to password login", "error_recovery": "Helpful error messages with solutions", "session_management": "Automatic logout on security events"}, "accessibility": {"screen_readers": "Full VoiceOver/TalkBack support", "high_contrast": "Support for high contrast mode", "font_scaling": "Respect system font size settings", "motor_accessibility": "Support for switch control and assistive touch"}}, "state_management": {"authentication_state": {"user": "Current user object", "is_authenticated": "boolean", "is_biometric_enabled": "boolean", "is_offline": "boolean", "loading_states": "various loading flags", "error_state": "error object"}, "device_state": {"device_id": "unique device identifier", "biometric_available": "boolean", "biometric_enrolled": "boolean", "network_status": "online/offline", "app_state": "foreground/background"}}, "internationalization": {"supported_languages": ["en", "ja", "zh", "vi"], "text_keys": {"app_name": "Smart Factory WMS", "welcome_back": "Welcome Back", "username_label": "Username or Email", "password_label": "Password", "sign_in": "Sign In", "sign_in_with_biometric": "Sign in with {biometric_type}", "remember_device": "Remember this device", "forgot_password": "Forgot Password?", "biometric_prompt_title": "Authenticate", "biometric_prompt_subtitle": "Use your {biometric_type} to sign in", "biometric_error": "Biometric authentication failed", "offline_mode": "You're offline", "sync_in_progress": "Syncing data...", "network_error": "Network connection error", "invalid_credentials": "Invalid username or password", "account_locked": "Account is locked", "session_expired": "Session expired, please sign in again", "biometric_not_available": "Biometric authentication not available", "biometric_not_enrolled": "No biometric data enrolled on device"}, "platform_specific": {"ios": "Use iOS-specific terminology (Face ID, Touch ID)", "android": "Use Android-specific terminology (Fingerprint, Face unlock)"}}, "performance_requirements": ["App launch time < 3 seconds", "Login response time < 2 seconds", "Biometric authentication < 1 second", "Smooth animations at 60fps", "Memory usage < 100MB", "Battery optimization for background tasks"], "testing_requirements": {"unit_tests": ["Authentication logic", "Biometric integration", "Offline mode functionality", "State management", "Security utilities"], "widget_tests": ["Login screen UI", "Biometric prompt", "Error handling displays", "Accessibility features", "Responsive layouts"], "integration_tests": ["Complete authentication flow", "Biometric authentication", "Offline/online transitions", "API integration", "Platform-specific features"], "platform_tests": ["iOS-specific functionality", "Android-specific functionality", "Device-specific features", "Performance testing"]}, "security_testing": ["Penetration testing for authentication", "Biometric security validation", "Secure storage verification", "Network security testing", "Runtime security checks"], "implementation_notes": ["Use Flutter 3.x with latest stable version", "Implement platform-specific UI with adaptive widgets", "Use Riverpod for state management", "Implement secure storage with flutter_secure_storage", "Use local_auth for biometric authentication", "Implement proper error handling and logging", "Follow platform-specific design guidelines", "Optimize for performance and battery life", "Implement comprehensive testing strategy"], "deliverables": ["LoginScreen widget", "BiometricAuthScreen widget", "AuthenticationService class", "SecureStorageService class", "OfflineAuthService class", "DeviceInfoService class", "Authentication state providers", "Platform-specific implementations", "Unit and integration tests", "Documentation and setup guides"], "dependencies": {"flutter_packages": ["flutter_secure_storage: ^9.0.0", "local_auth: ^2.1.6", "dio: ^5.3.2", "riverpod: ^2.4.0", "device_info_plus: ^9.1.0", "connectivity_plus: ^4.0.2", "flutter_localizations: ^3.0.0", "shared_preferences: ^2.2.2"], "platform_specific": {"ios": "iOS 12.0+ for biometric features", "android": "Android API 23+ for fingerprint, API 28+ for face unlock"}}}