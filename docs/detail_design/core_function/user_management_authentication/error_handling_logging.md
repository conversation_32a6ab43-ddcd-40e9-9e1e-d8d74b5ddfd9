# User Management & Authentication - Error Handling & Logging

## 1. Error Code Catalog

### Authentication Errors (4000-4099)

| Error Code | Error Type | Description | HTTP Status | Recovery Action |
|------------|------------|-------------|-------------|-----------------|
| 4001 | AUTHENTICATION_FAILED | Invalid username or password | 401 | Verify credentials and retry |
| 4002 | TOKEN_EXPIRED | JWT access token has expired | 401 | Use refresh token to get new access token |
| 4003 | TOKEN_INVALID | JWT token is malformed or invalid | 401 | Re-authenticate to get new token |
| 4004 | TOKEN_BLACKLISTED | JWT token has been blacklisted | 401 | Re-authenticate to get new token |
| 4005 | REFRESH_TOKEN_EXPIRED | Refresh token has expired | 401 | Re-authenticate with credentials |
| 4006 | REFRESH_TOKEN_INVALID | Refresh token is invalid or revoked | 401 | Re-authenticate with credentials |
| 4007 | SESSION_EXPIRED | User session has expired | 401 | Re-authenticate to create new session |
| 4008 | SESSION_INVALID | Session ID is invalid or not found | 401 | Re-authenticate to create new session |
| 4009 | ACCOUNT_LOCKED | User account is temporarily locked | 423 | Wait for lockout period or contact admin |
| 4010 | ACCOUNT_DISABLED | User account is permanently disabled | 401 | Contact administrator for account activation |
| 4011 | ACCOUNT_NOT_VERIFIED | Email address not verified | 401 | Verify email address before login |
| 4012 | PASSWORD_EXPIRED | Password has expired | 401 | Change password before login |
| 4013 | MUST_CHANGE_PASSWORD | User must change password | 401 | Change password before proceeding |

### Authorization Errors (4100-4199)

| Error Code | Error Type | Description | HTTP Status | Recovery Action |
|------------|------------|-------------|-------------|-----------------|
| 4101 | INSUFFICIENT_PERMISSIONS | User lacks required permissions | 403 | Contact admin for permission assignment |
| 4102 | ROLE_NOT_ASSIGNED | Required role not assigned to user | 403 | Contact admin for role assignment |
| 4103 | SYSTEM_ACCESS_DENIED | User cannot access this system | 403 | Verify system access or contact admin |
| 4104 | RESOURCE_ACCESS_DENIED | Access to specific resource denied | 403 | Contact admin for resource permissions |
| 4105 | ACTION_NOT_PERMITTED | Specific action not permitted | 403 | Contact admin for action permissions |
| 4106 | IP_ADDRESS_BLOCKED | Request from blocked IP address | 403 | Contact admin to unblock IP address |
| 4107 | DEVICE_NOT_AUTHORIZED | Device not authorized for access | 403 | Register device or contact admin |

### User Management Errors (4200-4299)

| Error Code | Error Type | Description | HTTP Status | Recovery Action |
|------------|------------|-------------|-------------|-----------------|
| 4201 | USER_NOT_FOUND | User does not exist | 404 | Verify user ID or create new user |
| 4202 | USERNAME_EXISTS | Username already taken | 409 | Choose different username |
| 4203 | EMAIL_EXISTS | Email address already registered | 409 | Use different email or recover account |
| 4204 | INVALID_USERNAME | Username format is invalid | 400 | Use valid username format |
| 4205 | INVALID_EMAIL | Email format is invalid | 400 | Use valid email format |
| 4206 | WEAK_PASSWORD | Password doesn't meet policy | 400 | Use stronger password |
| 4207 | PASSWORD_REUSE | Password recently used | 400 | Choose different password |
| 4208 | INVALID_PHONE_NUMBER | Phone number format invalid | 400 | Use valid phone number format |
| 4209 | USER_CREATION_FAILED | Failed to create user account | 500 | Retry or contact support |
| 4210 | USER_UPDATE_FAILED | Failed to update user account | 500 | Retry or contact support |
| 4211 | USER_DELETION_FAILED | Failed to delete user account | 500 | Retry or contact support |

### Role & Permission Errors (4300-4399)

| Error Code | Error Type | Description | HTTP Status | Recovery Action |
|------------|------------|-------------|-------------|-----------------|
| 4301 | ROLE_NOT_FOUND | Role does not exist | 404 | Verify role ID or create new role |
| 4302 | PERMISSION_NOT_FOUND | Permission does not exist | 404 | Verify permission ID or create permission |
| 4303 | ROLE_NAME_EXISTS | Role name already exists | 409 | Choose different role name |
| 4304 | PERMISSION_NAME_EXISTS | Permission name already exists | 409 | Choose different permission name |
| 4305 | CANNOT_DELETE_SYSTEM_ROLE | System role cannot be deleted | 400 | Only custom roles can be deleted |
| 4306 | ROLE_IN_USE | Role assigned to users | 400 | Remove role from users before deletion |
| 4307 | PERMISSION_IN_USE | Permission assigned to roles | 400 | Remove permission from roles before deletion |
| 4308 | INVALID_ROLE_ASSIGNMENT | Invalid role assignment | 400 | Verify role and user compatibility |
| 4309 | INVALID_PERMISSION_ASSIGNMENT | Invalid permission assignment | 400 | Verify permission and role compatibility |

### System Errors (4400-4499)

| Error Code | Error Type | Description | HTTP Status | Recovery Action |
|------------|------------|-------------|-------------|-----------------|
| 4401 | RATE_LIMIT_EXCEEDED | Too many requests | 429 | Wait and retry with exponential backoff |
| 4402 | SERVICE_UNAVAILABLE | Authentication service unavailable | 503 | Retry after service recovery |
| 4403 | DATABASE_ERROR | Database connection or query error | 500 | Retry or contact support |
| 4404 | CACHE_ERROR | Redis cache error | 500 | Retry or contact support |
| 4405 | EMAIL_SERVICE_ERROR | Email service unavailable | 500 | Retry email operation later |
| 4406 | CONFIGURATION_ERROR | Invalid system configuration | 500 | Contact administrator |
| 4407 | ENCRYPTION_ERROR | Encryption/decryption failed | 500 | Contact support |
| 4408 | VALIDATION_ERROR | Input validation failed | 400 | Correct input and retry |
| 4409 | SYSTEM_CONFIG_ERROR | System configuration error | 500 | Contact administrator |
| 4410 | ACCESS_DENIED | Access denied | 403 | Contact administrator |

## 2. Error Response Format

### Standardized Error Response Structure

```json
{
  "error": {
    "code": 4001,
    "type": "AUTHENTICATION_FAILED",
    "message": "Invalid username or password",
    "details": {
      "field_errors": {
        "username": ["Username is required"],
        "password": ["Password must be at least 8 characters"]
      },
      "validation_errors": [
        {
          "field": "email",
          "message": "Invalid email format",
          "value": "invalid-email"
        }
      ]
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789",
    "trace_id": "trace_abc123def456"
  }
}
```

### Error Response Classes

```python
from dataclasses import dataclass
from typing import Optional, Dict, List, Any
from datetime import datetime
import uuid

@dataclass
class FieldError:
    field: str
    message: str
    value: Optional[Any] = None

@dataclass
class ErrorDetails:
    field_errors: Optional[Dict[str, List[str]]] = None
    validation_errors: Optional[List[FieldError]] = None
    context: Optional[Dict[str, Any]] = None

@dataclass
class ErrorResponse:
    code: int
    type: str
    message: str
    details: Optional[ErrorDetails] = None
    timestamp: datetime = None
    request_id: str = None
    trace_id: str = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.request_id is None:
            self.request_id = f"req_{uuid.uuid4().hex[:12]}"
        if self.trace_id is None:
            self.trace_id = f"trace_{uuid.uuid4().hex[:16]}"

class AuthenticationError(Exception):
    """Base authentication error"""
    def __init__(self, code: int, type: str, message: str, details: Optional[ErrorDetails] = None):
        self.error_response = ErrorResponse(code, type, message, details)
        super().__init__(message)

class AuthorizationError(Exception):
    """Base authorization error"""
    def __init__(self, code: int, type: str, message: str, details: Optional[ErrorDetails] = None):
        self.error_response = ErrorResponse(code, type, message, details)
        super().__init__(message)

class ValidationError(Exception):
    """Input validation error"""
    def __init__(self, field_errors: Dict[str, List[str]]):
        details = ErrorDetails(field_errors=field_errors)
        self.error_response = ErrorResponse(4408, "VALIDATION_ERROR", "Input validation failed", details)
        super().__init__("Input validation failed")

class UserManagementError(Exception):
    """User management operation error"""
    def __init__(self, code: int, type: str, message: str, details: Optional[ErrorDetails] = None):
        self.error_response = ErrorResponse(code, type, message, details)
        super().__init__(message)
```

### Error Handler Middleware

```python
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import logging

logger = logging.getLogger(__name__)

async def error_handler_middleware(request: Request, call_next):
    """Global error handling middleware"""
    try:
        response = await call_next(request)
        return response
    except AuthenticationError as e:
        logger.warning(f"Authentication error: {e.error_response.type}", extra={
            "error_code": e.error_response.code,
            "request_id": e.error_response.request_id,
            "user_agent": request.headers.get("user-agent"),
            "ip_address": request.client.host
        })
        return JSONResponse(
            status_code=401,
            content={"error": e.error_response.__dict__}
        )
    except AuthorizationError as e:
        logger.warning(f"Authorization error: {e.error_response.type}", extra={
            "error_code": e.error_response.code,
            "request_id": e.error_response.request_id,
            "user_id": getattr(request.state, "user_id", None)
        })
        return JSONResponse(
            status_code=403,
            content={"error": e.error_response.__dict__}
        )
    except ValidationError as e:
        logger.info(f"Validation error: {e.error_response.message}", extra={
            "field_errors": e.error_response.details.field_errors,
            "request_id": e.error_response.request_id
        })
        return JSONResponse(
            status_code=400,
            content={"error": e.error_response.__dict__}
        )
    except Exception as e:
        error_id = uuid.uuid4().hex[:12]
        logger.error(f"Unexpected error: {str(e)}", extra={
            "error_id": error_id,
            "exception_type": type(e).__name__,
            "traceback": traceback.format_exc()
        })
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": 5000,
                    "type": "INTERNAL_SERVER_ERROR",
                    "message": "An unexpected error occurred",
                    "error_id": error_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        )
```

## 3. Logging Strategy

### Log Levels and Categories

#### Log Levels
- **DEBUG**: Detailed diagnostic information for development
- **INFO**: General operational information
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error conditions that don't stop the application
- **CRITICAL**: Critical errors that may stop the application

#### Log Categories

| Category | Description | Log Level | Examples |
|----------|-------------|-----------|----------|
| **Authentication** | User login/logout events | INFO, WARNING | Successful login, failed login attempts |
| **Authorization** | Permission checks and access control | INFO, WARNING | Permission denied, role assignment |
| **Security** | Security-related events | WARNING, ERROR | Account lockout, suspicious activity |
| **User Management** | User CRUD operations | INFO | User creation, profile updates |
| **System** | System-level events | INFO, ERROR | Service startup, database connections |
| **Performance** | Performance metrics | INFO, WARNING | Slow queries, high response times |
| **Audit** | Compliance and audit events | INFO | All user actions for compliance |

### Structured Logging Format

```python
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging"""

    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add extra fields if present
        if hasattr(record, "user_id"):
            log_entry["user_id"] = record.user_id
        if hasattr(record, "tenant_id"):
            log_entry["tenant_id"] = record.tenant_id
        if hasattr(record, "session_id"):
            log_entry["session_id"] = record.session_id
        if hasattr(record, "request_id"):
            log_entry["request_id"] = record.request_id
        if hasattr(record, "trace_id"):
            log_entry["trace_id"] = record.trace_id
        if hasattr(record, "ip_address"):
            log_entry["ip_address"] = record.ip_address
        if hasattr(record, "user_agent"):
            log_entry["user_agent"] = record.user_agent
        if hasattr(record, "action"):
            log_entry["action"] = record.action
        if hasattr(record, "resource"):
            log_entry["resource"] = record.resource
        if hasattr(record, "duration_ms"):
            log_entry["duration_ms"] = record.duration_ms

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry)

# Logger configuration
def setup_logging():
    """Configure structured logging"""

    # Create formatters
    structured_formatter = StructuredFormatter()

    # Console handler for development
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(structured_formatter)
    console_handler.setLevel(logging.INFO)

    # File handler for local development
    file_handler = logging.FileHandler("app.log")
    file_handler.setFormatter(structured_formatter)
    file_handler.setLevel(logging.DEBUG)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # Configure specific loggers
    auth_logger = logging.getLogger("auth")
    auth_logger.setLevel(logging.INFO)

    security_logger = logging.getLogger("security")
    security_logger.setLevel(logging.WARNING)

    audit_logger = logging.getLogger("audit")
    audit_logger.setLevel(logging.INFO)
```

### Audit Logging Implementation

```python
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, Any
import logging

class AuditAction(Enum):
    """Audit action types"""
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_ACTIVATE = "user_activate"
    USER_DEACTIVATE = "user_deactivate"
    ROLE_ASSIGN = "role_assign"
    ROLE_REMOVE = "role_remove"
    PERMISSION_GRANT = "permission_grant"
    PERMISSION_REVOKE = "permission_revoke"
    ACCOUNT_LOCK = "account_lock"
    ACCOUNT_UNLOCK = "account_unlock"

@dataclass
class AuditContext:
    user_id: Optional[int]
    tenant_id: int
    session_id: Optional[str]
    ip_address: str
    user_agent: str
    request_id: str

class AuditLogger:
    """Centralized audit logging"""

    def __init__(self):
        self.logger = logging.getLogger("audit")

    def log_authentication_event(
        self,
        action: AuditAction,
        context: AuditContext,
        success: bool,
        details: Optional[Dict[str, Any]] = None
    ):
        """Log authentication-related events"""
        self.logger.info(
            f"Authentication event: {action.value}",
            extra={
                "category": "authentication",
                "action": action.value,
                "success": success,
                "user_id": context.user_id,
                "tenant_id": context.tenant_id,
                "session_id": context.session_id,
                "ip_address": context.ip_address,
                "user_agent": context.user_agent,
                "request_id": context.request_id,
                "details": details or {}
            }
        )

    def log_user_management_event(
        self,
        action: AuditAction,
        context: AuditContext,
        target_user_id: int,
        success: bool,
        details: Optional[Dict[str, Any]] = None
    ):
        """Log user management events"""
        self.logger.info(
            f"User management event: {action.value}",
            extra={
                "category": "user_management",
                "action": action.value,
                "success": success,
                "user_id": context.user_id,
                "target_user_id": target_user_id,
                "tenant_id": context.tenant_id,
                "ip_address": context.ip_address,
                "user_agent": context.user_agent,
                "request_id": context.request_id,
                "details": details or {}
            }
        )

    def log_security_event(
        self,
        event_type: str,
        context: AuditContext,
        severity: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """Log security events"""
        log_level = getattr(logging, severity.upper(), logging.WARNING)
        self.logger.log(
            log_level,
            f"Security event: {event_type}",
            extra={
                "category": "security",
                "event_type": event_type,
                "severity": severity,
                "user_id": context.user_id,
                "tenant_id": context.tenant_id,
                "ip_address": context.ip_address,
                "user_agent": context.user_agent,
                "request_id": context.request_id,
                "details": details or {}
            }
        )
```

### Performance Logging

```python
import time
from functools import wraps
from typing import Callable, Any
import logging

performance_logger = logging.getLogger("performance")

def log_performance(operation: str):
    """Decorator to log operation performance"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000

                performance_logger.info(
                    f"Operation completed: {operation}",
                    extra={
                        "operation": operation,
                        "duration_ms": round(duration_ms, 2),
                        "success": True
                    }
                )

                # Log warning for slow operations
                if duration_ms > 1000:  # 1 second threshold
                    performance_logger.warning(
                        f"Slow operation detected: {operation}",
                        extra={
                            "operation": operation,
                            "duration_ms": round(duration_ms, 2),
                            "threshold_exceeded": True
                        }
                    )

                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                performance_logger.error(
                    f"Operation failed: {operation}",
                    extra={
                        "operation": operation,
                        "duration_ms": round(duration_ms, 2),
                        "success": False,
                        "error": str(e)
                    }
                )
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000

                performance_logger.info(
                    f"Operation completed: {operation}",
                    extra={
                        "operation": operation,
                        "duration_ms": round(duration_ms, 2),
                        "success": True
                    }
                )

                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                performance_logger.error(
                    f"Operation failed: {operation}",
                    extra={
                        "operation": operation,
                        "duration_ms": round(duration_ms, 2),
                        "success": False,
                        "error": str(e)
                    }
                )
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# Usage examples
@log_performance("user_authentication")
async def authenticate_user(username: str, password: str) -> AuthResult:
    # Authentication logic
    pass

@log_performance("user_creation")
async def create_user(user_data: CreateUserRequest) -> User:
    # User creation logic
    pass
```

## 4. Monitoring and Alerting

### Key Metrics to Monitor

#### Authentication Metrics
- **Login Success Rate**: Percentage of successful login attempts
- **Login Failure Rate**: Percentage of failed login attempts
- **Account Lockout Rate**: Number of accounts locked per hour
- **Token Validation Rate**: Success rate of JWT token validations
- **Session Duration**: Average session length
- **Concurrent Sessions**: Number of active sessions

#### Performance Metrics
- **Response Time**: API endpoint response times
- **Database Query Time**: Database operation performance
- **Cache Hit Rate**: Redis cache effectiveness
- **Throughput**: Requests per second
- **Error Rate**: Percentage of requests resulting in errors

#### Security Metrics
- **Failed Login Attempts**: Consecutive failed attempts per user/IP
- **Suspicious Activity**: Unusual login patterns
- **Password Reset Requests**: Frequency of password resets
- **Token Blacklist Size**: Number of blacklisted tokens
- **Brute Force Attempts**: Detected brute force attacks

### CloudWatch Metrics Configuration

```python
import boto3
from datetime import datetime
from typing import Dict, Any

class CloudWatchMetrics:
    """CloudWatch metrics publisher"""

    def __init__(self, namespace: str = "SmartFactoryWMS/Authentication"):
        self.cloudwatch = boto3.client('cloudwatch')
        self.namespace = namespace

    def put_metric(
        self,
        metric_name: str,
        value: float,
        unit: str = "Count",
        dimensions: Dict[str, str] = None
    ):
        """Publish metric to CloudWatch"""
        try:
            self.cloudwatch.put_metric_data(
                Namespace=self.namespace,
                MetricData=[
                    {
                        'MetricName': metric_name,
                        'Value': value,
                        'Unit': unit,
                        'Timestamp': datetime.utcnow(),
                        'Dimensions': [
                            {'Name': k, 'Value': v}
                            for k, v in (dimensions or {}).items()
                        ]
                    }
                ]
            )
        except Exception as e:
            logging.error(f"Failed to publish metric {metric_name}: {e}")

    def increment_counter(self, metric_name: str, dimensions: Dict[str, str] = None):
        """Increment a counter metric"""
        self.put_metric(metric_name, 1, "Count", dimensions)

    def record_duration(self, metric_name: str, duration_ms: float, dimensions: Dict[str, str] = None):
        """Record duration metric"""
        self.put_metric(metric_name, duration_ms, "Milliseconds", dimensions)

    def record_gauge(self, metric_name: str, value: float, dimensions: Dict[str, str] = None):
        """Record gauge metric"""
        self.put_metric(metric_name, value, "None", dimensions)

# Metrics integration in services
class MetricsMiddleware:
    """Middleware to collect and publish metrics"""

    def __init__(self):
        self.metrics = CloudWatchMetrics()

    async def __call__(self, request: Request, call_next):
        start_time = time.time()

        try:
            response = await call_next(request)
            duration_ms = (time.time() - start_time) * 1000

            # Record response time
            self.metrics.record_duration(
                "ResponseTime",
                duration_ms,
                {
                    "Endpoint": request.url.path,
                    "Method": request.method,
                    "StatusCode": str(response.status_code)
                }
            )

            # Record request count
            self.metrics.increment_counter(
                "RequestCount",
                {
                    "Endpoint": request.url.path,
                    "Method": request.method,
                    "StatusCode": str(response.status_code)
                }
            )

            return response

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000

            # Record error metrics
            self.metrics.record_duration(
                "ResponseTime",
                duration_ms,
                {
                    "Endpoint": request.url.path,
                    "Method": request.method,
                    "StatusCode": "500"
                }
            )

            self.metrics.increment_counter(
                "ErrorCount",
                {
                    "Endpoint": request.url.path,
                    "Method": request.method,
                    "ErrorType": type(e).__name__
                }
            )

            raise
```

### Alert Configuration

#### CloudWatch Alarms

```yaml
# CloudWatch Alarms Configuration
alarms:
  high_error_rate:
    metric_name: "ErrorRate"
    threshold: 5.0  # 5% error rate
    comparison: "GreaterThanThreshold"
    evaluation_periods: 2
    period: 300  # 5 minutes
    statistic: "Average"
    alarm_actions:
      - "arn:aws:sns:region:account:auth-alerts"

  slow_response_time:
    metric_name: "ResponseTime"
    threshold: 2000  # 2 seconds
    comparison: "GreaterThanThreshold"
    evaluation_periods: 3
    period: 300
    statistic: "Average"
    alarm_actions:
      - "arn:aws:sns:region:account:performance-alerts"

  high_failed_login_rate:
    metric_name: "FailedLoginRate"
    threshold: 10.0  # 10% failed login rate
    comparison: "GreaterThanThreshold"
    evaluation_periods: 2
    period: 300
    statistic: "Average"
    alarm_actions:
      - "arn:aws:sns:region:account:security-alerts"

  account_lockout_spike:
    metric_name: "AccountLockouts"
    threshold: 10  # 10 lockouts per 5 minutes
    comparison: "GreaterThanThreshold"
    evaluation_periods: 1
    period: 300
    statistic: "Sum"
    alarm_actions:
      - "arn:aws:sns:region:account:security-alerts"

  database_connection_errors:
    metric_name: "DatabaseErrors"
    threshold: 1
    comparison: "GreaterThanOrEqualToThreshold"
    evaluation_periods: 1
    period: 60
    statistic: "Sum"
    alarm_actions:
      - "arn:aws:sns:region:account:critical-alerts"
```

#### Custom Alert Rules

```python
from dataclasses import dataclass
from typing import List, Callable
from enum import Enum

class AlertSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AlertRule:
    name: str
    condition: Callable
    severity: AlertSeverity
    message: str
    cooldown_minutes: int = 15

class SecurityAlertManager:
    """Manage security-related alerts"""

    def __init__(self):
        self.alert_rules = [
            AlertRule(
                name="brute_force_attack",
                condition=self._check_brute_force,
                severity=AlertSeverity.HIGH,
                message="Potential brute force attack detected",
                cooldown_minutes=5
            ),
            AlertRule(
                name="unusual_login_pattern",
                condition=self._check_unusual_login,
                severity=AlertSeverity.MEDIUM,
                message="Unusual login pattern detected",
                cooldown_minutes=30
            ),
            AlertRule(
                name="mass_account_lockout",
                condition=self._check_mass_lockout,
                severity=AlertSeverity.CRITICAL,
                message="Mass account lockout detected",
                cooldown_minutes=5
            )
        ]
        self.alert_history = {}

    def _check_brute_force(self, context: Dict[str, Any]) -> bool:
        """Check for brute force attack patterns"""
        failed_attempts = context.get("failed_attempts_last_hour", 0)
        unique_ips = context.get("unique_ips_failed", 0)

        # Alert if more than 100 failed attempts from less than 10 IPs
        return failed_attempts > 100 and unique_ips < 10

    def _check_unusual_login(self, context: Dict[str, Any]) -> bool:
        """Check for unusual login patterns"""
        login_times = context.get("login_times", [])
        locations = context.get("login_locations", [])

        # Alert if logins from multiple countries within 1 hour
        return len(set(locations)) > 2 and len(login_times) > 0

    def _check_mass_lockout(self, context: Dict[str, Any]) -> bool:
        """Check for mass account lockouts"""
        lockouts_last_hour = context.get("lockouts_last_hour", 0)

        # Alert if more than 50 accounts locked in 1 hour
        return lockouts_last_hour > 50

    async def evaluate_alerts(self, context: Dict[str, Any]):
        """Evaluate all alert rules"""
        for rule in self.alert_rules:
            if self._should_evaluate(rule) and rule.condition(context):
                await self._trigger_alert(rule, context)

    def _should_evaluate(self, rule: AlertRule) -> bool:
        """Check if rule should be evaluated (cooldown check)"""
        last_triggered = self.alert_history.get(rule.name)
        if last_triggered is None:
            return True

        cooldown_seconds = rule.cooldown_minutes * 60
        return (datetime.utcnow() - last_triggered).seconds > cooldown_seconds

    async def _trigger_alert(self, rule: AlertRule, context: Dict[str, Any]):
        """Trigger alert notification"""
        self.alert_history[rule.name] = datetime.utcnow()

        alert_data = {
            "rule_name": rule.name,
            "severity": rule.severity.value,
            "message": rule.message,
            "context": context,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Send to appropriate notification channels based on severity
        if rule.severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL]:
            await self._send_immediate_notification(alert_data)
        else:
            await self._send_standard_notification(alert_data)

    async def _send_immediate_notification(self, alert_data: Dict[str, Any]):
        """Send immediate notification for high/critical alerts"""
        # Implementation for immediate notifications (SMS, Slack, etc.)
        pass

    async def _send_standard_notification(self, alert_data: Dict[str, Any]):
        """Send standard notification for low/medium alerts"""
        # Implementation for standard notifications (email, etc.)
        pass
```

### Log Retention and Storage

#### Log Retention Policies

| Log Type | Retention Period | Storage Location | Compression |
|----------|------------------|------------------|-------------|
| **Application Logs** | 90 days | CloudWatch Logs | Gzip |
| **Audit Logs** | 7 years | S3 + Glacier | Gzip |
| **Security Logs** | 2 years | S3 | Gzip |
| **Performance Logs** | 30 days | CloudWatch Logs | Gzip |
| **Debug Logs** | 7 days | Local/CloudWatch | Gzip |

#### Log Archival Strategy

```python
import boto3
from datetime import datetime, timedelta

class LogArchivalManager:
    """Manage log archival and retention"""

    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.cloudwatch_client = boto3.client('logs')

    async def archive_audit_logs(self, days_old: int = 90):
        """Archive audit logs older than specified days to S3"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)

        # Query audit logs from database
        old_logs = await self._get_old_audit_logs(cutoff_date)

        # Archive to S3
        for log_batch in self._batch_logs(old_logs, 1000):
            await self._upload_to_s3(log_batch, "audit-logs")

        # Remove from database after successful archival
        await self._cleanup_old_audit_logs(cutoff_date)

    async def setup_cloudwatch_retention(self):
        """Set up CloudWatch log retention policies"""
        log_groups = [
            ("smart-factory-auth", 90),  # 90 days
            ("smart-factory-security", 730),  # 2 years
            ("smart-factory-performance", 30),  # 30 days
        ]

        for log_group, retention_days in log_groups:
            try:
                self.cloudwatch_client.put_retention_policy(
                    logGroupName=log_group,
                    retentionInDays=retention_days
                )
            except Exception as e:
                logging.error(f"Failed to set retention for {log_group}: {e}")
```

This comprehensive error handling and logging strategy ensures robust monitoring, debugging, and compliance capabilities for the Smart Factory WMS authentication system.
```
```