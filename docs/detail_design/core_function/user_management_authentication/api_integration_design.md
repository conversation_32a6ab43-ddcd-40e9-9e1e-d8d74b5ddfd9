# User Management & Authentication - API & Integration Design

## 1. Service-to-Service APIs

### Authentication Service API

#### Base Configuration
- **Base URL**: `https://api.smartfactory.com/auth/v1`
- **Protocol**: HTTPS only
- **Content-Type**: `application/json`
- **Authentication**: Bearer JWT tokens (except for login endpoints)
- **Rate Limiting**: 100 requests/minute per IP, 1000 requests/minute per authenticated user

#### OpenAPI Specification

```yaml
openapi: 3.0.3
info:
  title: Smart Factory WMS - Authentication Service API
  description: User authentication and authorization service
  version: 1.0.0
  contact:
    name: Smart Factory WMS Team
    email: <EMAIL>

servers:
  - url: https://api.smartfactory.com/auth/v1
    description: Production server
  - url: https://staging-api.smartfactory.com/auth/v1
    description: Staging server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          minLength: 3
          maxLength: 50
          example: "john.doe"
        password:
          type: string
          minLength: 8
          maxLength: 128
          format: password
          example: "SecurePass123!"
        remember_me:
          type: boolean
          default: false
          example: true

    LoginResponse:
      type: object
      properties:
        access_token:
          type: string
          description: JWT access token
          example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
        refresh_token:
          type: string
          description: Refresh token for obtaining new access tokens
          example: "rt_1234567890abcdef..."
        token_type:
          type: string
          enum: ["Bearer"]
          example: "Bearer"
        expires_in:
          type: integer
          description: Access token expiration time in seconds
          example: 1800
        user:
          $ref: '#/components/schemas/UserProfile'

    UserProfile:
      type: object
      properties:
        user_id:
          type: integer
          example: 12345
        username:
          type: string
          example: "john.doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"
        preferred_language:
          type: string
          enum: ["en", "ja", "zh", "vi"]
          example: "en"
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        permissions:
          type: array
          items:
            type: string
          example: ["users:read", "inventory:manage"]
        last_login:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    Role:
      type: object
      properties:
        role_id:
          type: integer
          example: 1
        role_name:
          type: string
          example: "Warehouse Manager"
        description:
          type: string
          example: "Manages warehouse operations and staff"

    RefreshTokenRequest:
      type: object
      required:
        - refresh_token
      properties:
        refresh_token:
          type: string
          example: "rt_1234567890abcdef..."

    PasswordChangeRequest:
      type: object
      required:
        - current_password
        - new_password
      properties:
        current_password:
          type: string
          format: password
          example: "OldPass123!"
        new_password:
          type: string
          format: password
          minLength: 8
          example: "NewSecurePass456!"

    PasswordResetRequest:
      type: object
      required:
        - email
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"

    PasswordResetConfirm:
      type: object
      required:
        - token
        - new_password
      properties:
        token:
          type: string
          example: "reset_token_123456"
        new_password:
          type: string
          format: password
          minLength: 8
          example: "NewPassword789!"

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          example: "invalid_credentials"
        error_description:
          type: string
          example: "The provided credentials are invalid"
        error_code:
          type: integer
          example: 4001
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        request_id:
          type: string
          example: "req_123456789"

paths:
  /login:
    post:
      summary: User login
      description: Authenticate user and return JWT tokens
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '423':
          description: Account locked
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Too many requests
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /logout:
    post:
      summary: User logout
      description: Invalidate current session and tokens
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Logout successful"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /refresh:
    post:
      summary: Refresh access token
      description: Get new access token using refresh token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
                  token_type:
                    type: string
                    example: "Bearer"
                  expires_in:
                    type: integer
                    example: 1800
        '400':
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /me:
    get:
      summary: Get current user profile
      description: Retrieve authenticated user's profile information
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /change-password:
    post:
      summary: Change user password
      description: Change the current user's password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordChangeRequest'
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Password changed successfully"
        '400':
          description: Invalid request or password policy violation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Current password is incorrect
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reset-password:
    post:
      summary: Request password reset
      description: Send password reset email to user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetRequest'
      responses:
        '200':
          description: Password reset email sent
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Password reset email sent"
        '400':
          description: Invalid email address
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reset-password/confirm:
    post:
      summary: Confirm password reset
      description: Reset password using reset token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirm'
      responses:
        '200':
          description: Password reset successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Password reset successfully"
        '400':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /validate:
    post:
      summary: Validate JWT token
      description: Validate JWT token and return user information (for service-to-service calls)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - token
              properties:
                token:
                  type: string
                  example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
      responses:
        '200':
          description: Token is valid
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    example: true
                  user:
                    $ref: '#/components/schemas/UserProfile'
        '400':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: "Token expired"
```

### User Management Service API

#### User CRUD Operations

```yaml
paths:
  /users:
    get:
      summary: List users
      description: Get paginated list of users
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: search
          in: query
          schema:
            type: string
            description: Search by username, email, or name
        - name: role
          in: query
          schema:
            type: string
            description: Filter by role name
        - name: active
          in: query
          schema:
            type: boolean
            description: Filter by active status
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/UserProfile'
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                        example: 1
                      limit:
                        type: integer
                        example: 20
                      total:
                        type: integer
                        example: 150
                      pages:
                        type: integer
                        example: 8

    post:
      summary: Create user
      description: Create a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - email
                - password
                - first_name
                - last_name
              properties:
                username:
                  type: string
                  minLength: 3
                  maxLength: 50
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                first_name:
                  type: string
                  maxLength: 50
                last_name:
                  type: string
                  maxLength: 50
                phone_number:
                  type: string
                  maxLength: 20
                preferred_language:
                  type: string
                  enum: ["en", "ja", "zh", "vi"]
                  default: "en"
                roles:
                  type: array
                  items:
                    type: integer
                  description: Array of role IDs to assign
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '400':
          description: Invalid request data
        '409':
          description: Username or email already exists

  /users/{user_id}:
    get:
      summary: Get user by ID
      description: Retrieve specific user information
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '404':
          description: User not found

    put:
      summary: Update user
      description: Update user information
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  maxLength: 50
                last_name:
                  type: string
                  maxLength: 50
                email:
                  type: string
                  format: email
                phone_number:
                  type: string
                  maxLength: 20
                preferred_language:
                  type: string
                  enum: ["en", "ja", "zh", "vi"]
                is_active:
                  type: boolean
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '400':
          description: Invalid request data
        '404':
          description: User not found
        '409':
          description: Email already exists

    delete:
      summary: Delete user
      description: Soft delete user account
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: User deleted successfully
        '404':
          description: User not found
        '409':
          description: Cannot delete user with active sessions
```

## 2. Authentication Flows

### Login Flow Sequence

```mermaid
sequenceDiagram
    participant Client
    participant APIGateway
    participant AuthService
    participant UserService
    participant Database
    participant Redis
    participant AuditService

    Client->>APIGateway: POST /auth/login {username, password}
    APIGateway->>AuthService: Forward login request

    AuthService->>UserService: Validate credentials
    UserService->>Database: Query user by username
    Database-->>UserService: Return user data

    alt User not found
        UserService-->>AuthService: User not found
        AuthService->>AuditService: Log failed attempt
        AuthService-->>APIGateway: 404 User not found
        APIGateway-->>Client: 404 Error response
    else User found but inactive
        UserService-->>AuthService: User inactive
        AuthService->>AuditService: Log failed attempt
        AuthService-->>APIGateway: 401 Account disabled
        APIGateway-->>Client: 401 Error response
    else Account locked
        UserService-->>AuthService: Account locked
        AuthService->>AuditService: Log failed attempt
        AuthService-->>APIGateway: 423 Account locked
        APIGateway-->>Client: 423 Error response
    else Invalid password
        UserService->>UserService: Verify password hash
        UserService->>Database: Increment failed attempts
        UserService-->>AuthService: Invalid password
        AuthService->>AuditService: Log failed attempt
        AuthService-->>APIGateway: 401 Invalid credentials
        APIGateway-->>Client: 401 Error response
    else Valid credentials
        UserService->>Database: Reset failed attempts
        UserService->>Database: Update last_login
        UserService-->>AuthService: Authentication successful

        AuthService->>AuthService: Generate JWT access token
        AuthService->>AuthService: Generate refresh token
        AuthService->>Redis: Store refresh token
        AuthService->>Database: Create user session
        AuthService->>AuditService: Log successful login

        AuthService-->>APIGateway: Login response with tokens
        APIGateway-->>Client: 200 Success with tokens
    end
```

### Token Refresh Flow

```mermaid
sequenceDiagram
    participant Client
    participant APIGateway
    participant AuthService
    participant Redis
    participant Database

    Client->>APIGateway: POST /auth/refresh {refresh_token}
    APIGateway->>AuthService: Forward refresh request

    AuthService->>Redis: Validate refresh token
    alt Token not found or expired
        Redis-->>AuthService: Token invalid
        AuthService-->>APIGateway: 400 Invalid token
        APIGateway-->>Client: 400 Error response
    else Token valid
        Redis-->>AuthService: Token valid
        AuthService->>Database: Get user session

        alt Session not found or inactive
            Database-->>AuthService: Session invalid
            AuthService->>Redis: Remove refresh token
            AuthService-->>APIGateway: 401 Session expired
            APIGateway-->>Client: 401 Error response
        else Session valid
            Database-->>AuthService: Session data
            AuthService->>AuthService: Generate new access token
            AuthService->>AuthService: Generate new refresh token
            AuthService->>Redis: Store new refresh token
            AuthService->>Redis: Remove old refresh token
            AuthService->>Database: Update session last_accessed

            AuthService-->>APIGateway: New tokens
            APIGateway-->>Client: 200 Success with new tokens
        end
    end
```

### Authorization Flow

```mermaid
sequenceDiagram
    participant Client
    participant APIGateway
    participant AuthMiddleware
    participant AuthService
    participant Redis
    participant Database
    participant TargetService

    Client->>APIGateway: Request with Authorization header
    APIGateway->>AuthMiddleware: Validate request

    AuthMiddleware->>AuthMiddleware: Extract JWT token
    AuthMiddleware->>AuthService: Validate token

    AuthService->>AuthService: Verify token signature
    AuthService->>Redis: Check token blacklist

    alt Token blacklisted
        Redis-->>AuthService: Token blacklisted
        AuthService-->>AuthMiddleware: Token invalid
        AuthMiddleware-->>APIGateway: 401 Unauthorized
        APIGateway-->>Client: 401 Error response
    else Token valid
        Redis-->>AuthService: Token not blacklisted
        AuthService->>AuthService: Decode token claims
        AuthService->>Database: Get user permissions
        Database-->>AuthService: User permissions

        AuthService-->>AuthMiddleware: Token valid + user context
        AuthMiddleware->>AuthMiddleware: Check endpoint permissions

        alt Insufficient permissions
            AuthMiddleware-->>APIGateway: 403 Forbidden
            APIGateway-->>Client: 403 Error response
        else Permissions valid
            AuthMiddleware->>TargetService: Forward request with user context
            TargetService-->>AuthMiddleware: Service response
            AuthMiddleware-->>APIGateway: Forward response
            APIGateway-->>Client: Success response
        end
    end
```

## 3. Message Flows and Patterns

### Event-Driven Authentication Events

#### User Authentication Events

```json
{
  "event_type": "user.login.success",
  "timestamp": "2024-01-15T10:30:00Z",
  "user_id": 12345,
  "session_id": "sess_abc123",
  "metadata": {
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "device_info": "Desktop Chrome",
    "login_method": "password"
  }
}
```

```json
{
  "event_type": "user.login.failed",
  "timestamp": "2024-01-15T10:30:00Z",
  "username": "john.doe",
  "reason": "invalid_password",
  "metadata": {
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "failed_attempts": 3
  }
}
```

#### Account Security Events

```json
{
  "event_type": "user.account.locked",
  "timestamp": "2024-01-15T10:30:00Z",
  "user_id": 12345,
  "reason": "too_many_failed_attempts",
  "metadata": {
    "failed_attempts": 5,
    "locked_until": "2024-01-15T10:45:00Z",
    "ip_address": "*************"
  }
}
```

```json
{
  "event_type": "user.password.changed",
  "timestamp": "2024-01-15T10:30:00Z",
  "user_id": 12345,
  "changed_by": 12345,
  "metadata": {
    "ip_address": "*************",
    "forced_change": false
  }
}
```

### Service Integration Patterns

#### Authentication Middleware Pattern

```python
# FastAPI Dependency for Authentication
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> UserContext:
    """
    Validate JWT token and return user context
    """
    try:
        token = credentials.credentials
        user_context = await auth_service.validate_token(token)
        return user_context
    except InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def require_permission(permission: str):
    """
    Dependency factory for permission-based authorization
    """
    def permission_checker(
        user: UserContext = Depends(get_current_user)
    ) -> UserContext:
        if not user.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions: {permission} required"
            )
        return user
    return permission_checker

# Usage in endpoints
@app.get("/users")
async def list_users(
    user: UserContext = Depends(require_permission("users:read"))
):
    # Endpoint implementation
    pass
```

#### Service-to-Service Authentication

```python
# Internal service authentication
class ServiceAuthenticator:
    def __init__(self, service_name: str, secret_key: str):
        self.service_name = service_name
        self.secret_key = secret_key

    def generate_service_token(self) -> str:
        """Generate JWT token for service-to-service communication"""
        payload = {
            "iss": self.service_name,
            "aud": "smart-factory-wms",
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(minutes=5),
            "scope": "service"
        }
        return jwt.encode(payload, self.secret_key, algorithm="HS256")

    async def authenticate_request(self, token: str) -> bool:
        """Validate service token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])
            return payload.get("scope") == "service"
        except jwt.InvalidTokenError:
            return False
```

## 4. Error Handling Patterns

### Standardized Error Responses

```json
{
  "error": "authentication_failed",
  "error_description": "The provided credentials are invalid",
  "error_code": 4001,
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789",
  "details": {
    "field_errors": {
      "username": ["Username is required"],
      "password": ["Password must be at least 8 characters"]
    }
  }
}
```

### Error Code Catalog

| Error Code | Error Type | Description | HTTP Status |
|------------|------------|-------------|-------------|
| 4001 | authentication_failed | Invalid credentials | 401 |
| 4002 | token_expired | JWT token has expired | 401 |
| 4003 | token_invalid | JWT token is malformed or invalid | 401 |
| 4004 | account_locked | User account is temporarily locked | 423 |
| 4005 | account_disabled | User account is disabled | 401 |
| 4006 | insufficient_permissions | User lacks required permissions | 403 |
| 4007 | password_policy_violation | Password doesn't meet policy requirements | 400 |
| 4008 | user_not_found | User does not exist | 404 |
| 4009 | duplicate_username | Username already exists | 409 |
| 4010 | duplicate_email | Email already exists | 409 |
| 4011 | invalid_refresh_token | Refresh token is invalid or expired | 400 |
| 4012 | session_expired | User session has expired | 401 |
| 4013 | too_many_requests | Rate limit exceeded | 429 |

This API design ensures secure, scalable, and maintainable authentication and user management services for the Smart Factory WMS system.
```
```
```