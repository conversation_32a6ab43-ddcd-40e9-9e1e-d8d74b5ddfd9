# Smart Factory WMS - Documentation

This directory contains comprehensive documentation for the Smart Factory WMS project in multiple languages.

## Available Languages

### 🇺🇸 English
- [Project Document](en/01_project_document.md) - Overview and objectives
- [Development Plan](en/02_development_plan.md) - Development approach and phases
- [Architecture Document](en/03_architecture_document.md) - System architecture and components
- [Database Design](en/04_database_design.md) - Database schema and relationships
- [AI Development Issues Log](en/05_ai_development_issues.md) - AI-assisted development challenges and solutions
- [Coding Standards & Guidelines](en/06_coding_standards.md) - Coding rules and best practices
- [AI Prompt Templates](en/07_ai_prompt_template.md) - AI-assisted development templates
- [Feature Overview](en/08_feature_overview.md) - System features and user stories

### 🇯🇵 日本語 (Japanese)
- [プロジェクト文書](ja/01_project_document.md) - プロジェクト概要と目的
- [開発計画](ja/02_development_plan.md) - 開発アプローチとフェーズ
- [アーキテクチャ文書](ja/03_architecture_document.md) - システムアーキテクチャとコンポーネント
- [データベース設計](ja/04_database_design.md) - データベーススキーマと関係
- [AI開発課題ログ](ja/05_ai_development_issues.md) - AI支援開発の課題と解決策
- [コーディング標準・ガイドライン](ja/06_coding_standards.md) - コーディングルールとベストプラクティス
- [AIプロンプトテンプレート](ja/07_ai_prompt_template.md) - AI支援開発テンプレート
- [機能概要](ja/08_feature_overview.md) - システム機能とユーザーストーリー

## Document Structure

Each language directory contains the same set of documents with identical structure:

1. **01_project_document.md** - High-level project overview, objectives, and scope
2. **02_development_plan.md** - Development methodology, phases, and team structure
3. **03_architecture_document.md** - System architecture, components, and deployment
4. **04_database_design.md** - Database schema design and containerized deployment strategy
5. **05_ai_development_issues.md** - AI-assisted development challenges and solutions
6. **06_coding_standards.md** - Coding standards, best practices, and guidelines
7. **07_ai_prompt_template.md** - Comprehensive AI prompt engineering guide with templates and best practices
8. **08_feature_overview.md** - System features, user stories, and acceptance criteria

## Maintenance Guidelines

- **Primary Language**: English documents serve as the source of truth
- **Translation Updates**: Japanese documents should be updated when significant changes are made to English versions
- **Consistency**: Maintain consistent structure and formatting across all language versions
- **Technical Terms**: Use English technical terms with Japanese explanations where appropriate

## Contributing

When updating documentation:

1. Update the English version first
2. Update the Japanese version to maintain consistency
3. Ensure all cross-references and links are updated in both languages
4. Follow the established naming conventions and structure

## Language Codes

This project follows ISO 639-1 language codes:
- `en` - English
- `ja` - Japanese (日本語)

Future language additions should follow the same convention.
