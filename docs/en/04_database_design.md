# Smart Factory WMS - Database Design

## Database Strategy

The system uses a single-schema approach in PostgreSQL with an optimized table structure designed for efficient data management and scalability. The database is designed to support containerized deployments for individual customer instances.

### Database Design Principles
- **Single-schema approach**: Optimized table structure for efficient queries and data management
- **Connection pooling**: PgBouncer for efficient connection management
- **Indexing strategy**: Optimized indexes for frequent queries and performance
- **Data integrity**: Comprehensive constraints and foreign key relationships

## Core Tables

### User Management

| Table Name | Description |
|------------|-------------|
| users | User accounts and profiles |
| roles | Role definitions for permission management |
| permissions | Granular permissions for resource access control |
| user_roles | Many-to-many relationship between users and roles |
| role_permissions | Many-to-many relationship between roles and permissions |
| user_sessions | Active user sessions and refresh tokens |
| user_audit_logs | Audit trail for user activities |
| password_reset_tokens | Password reset token management |

### Master Data

| Table Name | Description |
|------------|-------------|
| products | Finished goods and semi-finished products |
| materials | Raw materials and packaging materials |
| locations | Warehouse locations (zones, racks, bins) |
| suppliers | Material and product suppliers |
| customers | Product customers |
| bom_headers | Bill of Materials headers |
| bom_details | Bill of Materials line items |

### Inventory Management

| Table Name | Description |
|------------|-------------|
| inventory | Current inventory levels by material/product and location |
| inventory_lots | Lot-specific inventory information |
| inventory_transactions | All inventory movements |
| inventory_adjustments | Manual adjustments to inventory |
| quality_inspections | Quality inspection results |

### Operations

| Table Name | Description |
|------------|-------------|
| receiving_orders | Incoming material orders |
| receiving_order_items | Line items for receiving orders |
| production_orders | Manufacturing orders |
| production_order_items | Materials required for production |
| production_results | Production output records |
| shipment_orders | Outgoing product orders |
| shipment_order_items | Line items for shipment orders |

## Entity Relationship Diagram (Mermaid)

```mermaid
erDiagram
    USERS }o--o{ ROLES : has
    ROLES }o--o{ PERMISSIONS : grants
    USERS ||--o{ INVENTORY_TRANSACTIONS : creates
    USERS ||--o{ USER_SESSIONS : has
    USERS ||--o{ USER_AUDIT_LOGS : generates

    MATERIALS ||--o{ BOM_DETAILS : used_in
    MATERIALS ||--o{ INVENTORY_LOTS : tracked_as
    MATERIALS ||--o{ RECEIVING_ORDER_ITEMS : received_as

    PRODUCTS ||--|| BOM_HEADERS : has
    PRODUCTS ||--o{ INVENTORY_LOTS : tracked_as
    PRODUCTS ||--o{ SHIPMENT_ORDER_ITEMS : shipped_as

    BOM_HEADERS ||--o{ BOM_DETAILS : contains

    INVENTORY_LOTS ||--o{ INVENTORY : stored_as
    INVENTORY_LOTS ||--o{ INVENTORY_TRANSACTIONS : moved_as

    LOCATIONS ||--o{ INVENTORY : stores
    LOCATIONS ||--o{ LOCATIONS : contains

    SUPPLIERS ||--o{ RECEIVING_ORDERS : supplies
    CUSTOMERS ||--o{ SHIPMENT_ORDERS : orders

    RECEIVING_ORDERS ||--o{ RECEIVING_ORDER_ITEMS : contains
    SHIPMENT_ORDERS ||--o{ SHIPMENT_ORDER_ITEMS : contains
    PRODUCTION_ORDERS ||--o{ PRODUCTION_ORDER_ITEMS : requires

    INVENTORY_TRANSACTIONS }o--o{ LOCATIONS : from_to
```

## Table Schemas

### User Management Tables

#### users

```sql
CREATE TABLE public.users (
    user_id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    phone_number VARCHAR(20),
    preferred_language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    must_change_password BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### roles

```sql
CREATE TABLE public.roles (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### permissions

```sql
CREATE TABLE public.permissions (
    permission_id SERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (resource, action)
);
```

#### user_roles

```sql
CREATE TABLE public.user_roles (
    user_id INTEGER NOT NULL REFERENCES public.users(user_id),
    role_id INTEGER NOT NULL REFERENCES public.roles(role_id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by INTEGER REFERENCES public.users(user_id),
    PRIMARY KEY (user_id, role_id)
);
```

#### role_permissions

```sql
CREATE TABLE public.role_permissions (
    role_id INTEGER NOT NULL REFERENCES public.roles(role_id),
    permission_id INTEGER NOT NULL REFERENCES public.permissions(permission_id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by INTEGER REFERENCES public.users(user_id),
    PRIMARY KEY (role_id, permission_id)
);
```

#### user_sessions

```sql
CREATE TABLE public.user_sessions (
    session_id VARCHAR(128) PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(user_id),
    refresh_token_hash VARCHAR(255) NOT NULL,
    device_info VARCHAR(200),
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### user_audit_logs

```sql
CREATE TABLE public.user_audit_logs (
    log_id BIGSERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES public.users(user_id),
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(50),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### password_reset_tokens

```sql
CREATE TABLE public.password_reset_tokens (
    token_id VARCHAR(128) PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(user_id),
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP WITH TIME ZONE
);
```

### Business Data Tables

The following tables contain the core business data for the smart factory WMS system.

#### materials

```sql
CREATE TABLE public.materials (
    material_id SERIAL PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL UNIQUE,
    material_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(20) NOT NULL, -- raw, packaging, etc.
    unit_of_measure VARCHAR(20) NOT NULL,
    minimum_stock NUMERIC(10, 2),
    reorder_point NUMERIC(10, 2),
    lead_time_days INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### products

```sql
CREATE TABLE public.products (
    product_id SERIAL PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE,
    product_name VARCHAR(100) NOT NULL,
    product_type VARCHAR(20) NOT NULL, -- finished, semi-finished, etc.
    unit_of_measure VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### locations

```sql
CREATE TABLE public.locations (
    location_id SERIAL PRIMARY KEY,
    location_code VARCHAR(50) NOT NULL UNIQUE,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(20) NOT NULL, -- zone, rack, bin, etc.
    parent_location_id INTEGER REFERENCES public.locations(location_id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### suppliers

```sql
CREATE TABLE public.suppliers (
    supplier_id SERIAL PRIMARY KEY,
    supplier_code VARCHAR(50) NOT NULL UNIQUE,
    supplier_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### customers

```sql
CREATE TABLE public.customers (
    customer_id SERIAL PRIMARY KEY,
    customer_code VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### bom_headers

```sql
CREATE TABLE public.bom_headers (
    bom_id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES public.products(product_id),
    bom_version VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (product_id, bom_version)
);
```

#### bom_details

```sql
CREATE TABLE public.bom_details (
    bom_detail_id SERIAL PRIMARY KEY,
    bom_id INTEGER NOT NULL REFERENCES public.bom_headers(bom_id),
    material_id INTEGER NOT NULL REFERENCES public.materials(material_id),
    quantity NUMERIC(10, 4) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### inventory_lots

```sql
CREATE TABLE public.inventory_lots (
    lot_id SERIAL PRIMARY KEY,
    lot_number VARCHAR(50) NOT NULL,
    material_id INTEGER REFERENCES public.materials(material_id),
    product_id INTEGER REFERENCES public.products(product_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    expiration_date DATE,
    production_date DATE,
    status VARCHAR(20) NOT NULL, -- available, quarantine, reserved, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    UNIQUE (lot_number, COALESCE(material_id, 0), COALESCE(product_id, 0))
);
```

#### inventory

```sql
CREATE TABLE public.inventory (
    inventory_id SERIAL PRIMARY KEY,
    material_id INTEGER REFERENCES public.materials(material_id),
    product_id INTEGER REFERENCES public.products(product_id),
    location_id INTEGER NOT NULL REFERENCES public.locations(location_id),
    lot_id INTEGER NOT NULL REFERENCES public.inventory_lots(lot_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    UNIQUE (COALESCE(material_id, 0), COALESCE(product_id, 0), location_id, lot_id)
);
```

#### inventory_transactions

```sql
CREATE TABLE public.inventory_transactions (
    transaction_id SERIAL PRIMARY KEY,
    transaction_type VARCHAR(20) NOT NULL, -- receiving, production, shipment, adjustment, etc.
    material_id INTEGER REFERENCES public.materials(material_id),
    product_id INTEGER REFERENCES public.products(product_id),
    lot_id INTEGER NOT NULL REFERENCES public.inventory_lots(lot_id),
    source_location_id INTEGER REFERENCES public.locations(location_id),
    destination_location_id INTEGER REFERENCES public.locations(location_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    reference_id INTEGER, -- ID of the related document (receiving, production, shipment)
    reference_type VARCHAR(50), -- Type of the related document
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL REFERENCES public.users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    CHECK (source_location_id IS NOT NULL OR destination_location_id IS NOT NULL)
);
```

#### receiving_orders

```sql
CREATE TABLE public.receiving_orders (
    receiving_id SERIAL PRIMARY KEY,
    receiving_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INTEGER NOT NULL REFERENCES public.suppliers(supplier_id),
    expected_date DATE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, partially_received, completed, cancelled
    created_by INTEGER NOT NULL REFERENCES public.users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### receiving_order_items

```sql
CREATE TABLE public.receiving_order_items (
    item_id SERIAL PRIMARY KEY,
    receiving_id INTEGER NOT NULL REFERENCES public.receiving_orders(receiving_id),
    material_id INTEGER NOT NULL REFERENCES public.materials(material_id),
    expected_quantity NUMERIC(10, 2) NOT NULL,
    received_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    lot_number VARCHAR(50),
    status VARCHAR(20) NOT NULL, -- pending, partially_received, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### shipment_orders

```sql
CREATE TABLE public.shipment_orders (
    shipment_id SERIAL PRIMARY KEY,
    shipment_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INTEGER NOT NULL REFERENCES public.customers(customer_id),
    expected_date DATE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, partially_shipped, completed, cancelled
    created_by INTEGER NOT NULL REFERENCES public.users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### shipment_order_items

```sql
CREATE TABLE public.shipment_order_items (
    item_id SERIAL PRIMARY KEY,
    shipment_id INTEGER NOT NULL REFERENCES public.shipment_orders(shipment_id),
    product_id INTEGER NOT NULL REFERENCES public.products(product_id),
    expected_quantity NUMERIC(10, 2) NOT NULL,
    shipped_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL, -- pending, partially_shipped, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### production_orders

```sql
CREATE TABLE public.production_orders (
    production_id SERIAL PRIMARY KEY,
    production_number VARCHAR(50) NOT NULL UNIQUE,
    product_id INTEGER NOT NULL REFERENCES public.products(product_id),
    bom_id INTEGER NOT NULL REFERENCES public.bom_headers(bom_id),
    planned_quantity NUMERIC(10, 2) NOT NULL,
    produced_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    planned_start_date DATE,
    planned_end_date DATE,
    actual_start_date TIMESTAMP WITH TIME ZONE,
    actual_end_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, in_progress, completed, cancelled
    created_by INTEGER NOT NULL REFERENCES public.users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### production_order_items

```sql
CREATE TABLE public.production_order_items (
    item_id SERIAL PRIMARY KEY,
    production_id INTEGER NOT NULL REFERENCES public.production_orders(production_id),
    material_id INTEGER NOT NULL REFERENCES public.materials(material_id),
    required_quantity NUMERIC(10, 2) NOT NULL,
    consumed_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL, -- pending, partially_consumed, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### production_results

```sql
CREATE TABLE public.production_results (
    result_id SERIAL PRIMARY KEY,
    production_id INTEGER NOT NULL REFERENCES public.production_orders(production_id),
    lot_number VARCHAR(50) NOT NULL,
    produced_quantity NUMERIC(10, 2) NOT NULL,
    defect_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    production_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    quality_status VARCHAR(20) NOT NULL, -- passed, failed, pending
    created_by INTEGER NOT NULL REFERENCES public.users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### quality_inspections

```sql
CREATE TABLE public.quality_inspections (
    inspection_id SERIAL PRIMARY KEY,
    inspection_type VARCHAR(20) NOT NULL, -- receiving, production, shipment
    reference_id INTEGER NOT NULL, -- ID of the related document
    reference_type VARCHAR(50) NOT NULL, -- Type of the related document
    material_id INTEGER REFERENCES public.materials(material_id),
    product_id INTEGER REFERENCES public.products(product_id),
    lot_id INTEGER REFERENCES public.inventory_lots(lot_id),
    inspection_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    inspector_id INTEGER NOT NULL REFERENCES public.users(user_id),
    result VARCHAR(20) NOT NULL, -- passed, failed, pending
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL)
);
```

## Database Implementation Notes

### 1. Database Design Strategy

**Single-Schema Approach**: The database uses a single, optimized schema design for efficient data management and query performance. This approach provides:
- Better query performance through optimized indexing
- Simplified database maintenance and backups
- Efficient connection pooling with PgBouncer
- Consistent data structure

**Query Optimization**: All queries are optimized with proper indexing and constraints:
```sql
-- Example optimized query
SELECT * FROM public.materials
WHERE material_code = %s;
```

### 2. Indexing Strategy

**Optimized indexes for frequent queries and performance**:

```sql
-- Primary business entity indexes
CREATE INDEX idx_materials_code ON public.materials(material_code);
CREATE INDEX idx_products_code ON public.products(product_code);
CREATE INDEX idx_inventory_location ON public.inventory(location_id);
CREATE INDEX idx_inventory_lots_status ON public.inventory_lots(status);

-- Composite indexes for frequent queries
CREATE INDEX idx_inventory_material_location ON public.inventory(material_id, location_id);
CREATE INDEX idx_inventory_product_location ON public.inventory(product_id, location_id);
CREATE INDEX idx_inventory_lot_location ON public.inventory(lot_id, location_id);

-- Performance indexes for transactions
CREATE INDEX idx_inventory_transactions_date ON public.inventory_transactions(transaction_date);
CREATE INDEX idx_inventory_transactions_type ON public.inventory_transactions(transaction_type);
```

### 3. Connection Management

**PgBouncer Configuration**: The application uses PgBouncer for efficient connection pooling:
- Session pooling for connection management
- Transaction pooling for high-performance operations
- Connection limits for resource management

### 4. Data Integrity and Constraints

**Foreign Key Relationships**: All tables maintain proper foreign key relationships with cascade options:
```sql
-- Example with proper cascade handling
ALTER TABLE public.inventory_lots
ADD CONSTRAINT fk_inventory_lots_material
FOREIGN KEY (material_id) REFERENCES public.materials(material_id) ON DELETE CASCADE;
```

**Check Constraints**: Business logic constraints are enforced at the database level:
```sql
-- Ensure either material_id or product_id is specified
CHECK (material_id IS NOT NULL OR product_id IS NOT NULL)
```

### 5. Query Optimization

**Optimized Queries**: All queries are optimized with proper indexing and constraints:
- Use appropriate indexes for WHERE clauses
- Leverage composite indexes for multi-column searches
- Implement efficient join strategies for cross-table queries

### 6. Per-Customer Deployment Strategy

**Database Instance Options**:
1. **Single Customer Database**: Individual PostgreSQL instance per customer deployment
2. **Containerized Deployment**: Each customer gets their own containerized database instance
3. **Dedicated Infrastructure**: Complete infrastructure separation per customer

**Connection Pooling**: PgBouncer configuration for single-customer deployment:
```ini
# PgBouncer configuration for single-customer deployment
[databases]
smart_factory_wms = host=localhost port=5432 dbname=smart_factory_wms
pool_mode = session
max_client_conn = 100
default_pool_size = 25
```
