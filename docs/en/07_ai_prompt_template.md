# Smart Factory WMS - AI Prompt Templates

## Purpose

This template provides a standardized approach for using AI coding assistants in the Smart Factory WMS project. This ensures consistent code quality, security compliance, and adherence to project standards.

## Basic Template Structure

```
# Smart Factory WMS - [Brief Task Description]

## Project Context
- Smart Factory WMS System
- [Related Architecture Components]
- [References to Related Existing Patterns/Files]

## Task Requirements
[Specific task description]

## Technical Requirements
- Implement proper authentication and authorization
- Follow project coding standards
- Implement appropriate error handling
- [Other technical requirements]

## Reference Code Examples
[Examples of related existing code]
```

## Usage Examples

### Backend Service Method

```
# Smart Factory WMS - New Receiving Service Method

## Project Context
- Smart Factory WMS System
- Part of receiving management module
- Follow service layer pattern

## Task Requirements
Create a service method to register new receiving based on receiving plans.
Need to generate receiving ID and create related inventory records.

## Technical Requirements
- Implement proper authentication and authorization
- Implement transaction management
- Include appropriate exception handling
- Implement logging
- Perform input validation

## Reference Code Examples
```python
async def create_shipment(self, shipment_data: ShipmentCreate, current_user: User) -> Shipment:
    # Authentication and authorization check
    if not current_user.has_permission('shipment:create'):
        raise PermissionError("Insufficient permissions to create shipment")
    
    async with self.db.transaction():
        try:
            # Processing logic
            # ...
            
            return created_shipment
        except Exception as e:
            self.logger.error(f"Shipment creation failed: {str(e)}")
            raise ServiceError(f"Failed to create shipment: {str(e)}")
```
```

### Frontend Component

```
# Smart Factory WMS - Inventory List Display Component

## Project Context
- React + TypeScript based frontend
- Responsive UI design
- Using Material-UI component library
- Part of inventory management module

## Task Requirements
Create a React component to display inventory items in a list.
Need to display data and include search and filtering functionality.

## Technical Requirements
- Ensure TypeScript type safety
- Handle authentication state properly
- Implement responsive design
- Handle error and loading states
- Accessibility compliance
- Internationalization (i18n) support

## Reference Code Examples
```typescript
interface InventoryListProps {
  onItemSelect?: (item: InventoryItem) => void;
  filters?: InventoryFilters;
}

const InventoryList: React.FC<InventoryListProps> = ({ onItemSelect, filters }) => {
  const { data, loading, error } = useInventoryQuery(filters);
  const { t } = useTranslation();
  const { user } = useAuth();
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!user?.hasPermission('inventory:read')) {
    return <AccessDeniedMessage />;
  }
  
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('inventory.itemCode')}</TableCell>
            <TableCell>{t('inventory.itemName')}</TableCell>
            <TableCell>{t('inventory.quantity')}</TableCell>
            <TableCell>{t('inventory.location')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data?.items.map((item) => (
            <TableRow key={item.id} onClick={() => onItemSelect?.(item)}>
              <TableCell>{item.code}</TableCell>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.quantity}</TableCell>
              <TableCell>{item.location}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
```
```

### Frontend Form

```
# Smart Factory WMS - Receiving Registration Form

## Project Context
- Form management using React Hook Form
- Validation using Zod
- Secure form processing
- Part of receiving management module

## Task Requirements
Create a form component for registering new receiving.
Need to include input validation, error display, and submission processing.

## Technical Requirements
- Form state management with React Hook Form
- Input validation with Zod schema
- Proper authentication and authorization verification
- Optimistic UI updates
- Error handling and user feedback
- Accessibility compliance

## Reference Code Examples
```typescript
const receivingSchema = z.object({
  planId: z.string().min(1, 'Plan ID is required'),
  items: z.array(z.object({
    itemCode: z.string().min(1, 'Item code is required'),
    expectedQuantity: z.number().min(1, 'Quantity must be positive'),
    actualQuantity: z.number().min(1, 'Actual quantity must be positive'),
  })).min(1, 'At least one item is required'),
});

type ReceivingFormData = z.infer<typeof receivingSchema>;

const ReceivingForm: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { mutate: submitReceiving, isLoading } = useReceivingMutation();
  
  const form = useForm<ReceivingFormData>({
    resolver: zodResolver(receivingSchema),
    defaultValues: {
      planId: '',
      items: [{ itemCode: '', expectedQuantity: 0, actualQuantity: 0 }],
    },
  });

  const onSubmit = (data: ReceivingFormData) => {
    if (!user?.hasPermission('receiving:create')) {
      toast.error(t('receiving.permissionDenied'));
      return;
    }
    
    submitReceiving(
      data,
      {
        onSuccess: () => {
          toast.success(t('receiving.submitSuccess'));
          form.reset();
        },
        onError: (error) => {
          toast.error(t('receiving.submitError', { error: error.message }));
        },
      }
    );
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <TextField
        {...form.register('planId')}
        label={t('receiving.planId')}
        error={!!form.formState.errors.planId}
        helperText={form.formState.errors.planId?.message}
        fullWidth
        margin="normal"
      />
      
      {/* Dynamic item list */}
      <FieldArray
        control={form.control}
        name="items"
        render={({ fields, append, remove }) => (
          <div>
            {fields.map((field, index) => (
              <div key={field.id}>
                <TextField
                  {...form.register(`items.${index}.itemCode`)}
                  label={t('receiving.itemCode')}
                  error={!!form.formState.errors.items?.[index]?.itemCode}
                  helperText={form.formState.errors.items?.[index]?.itemCode?.message}
                />
                {/* Other fields */}
              </div>
            ))}
            <Button onClick={() => append({ itemCode: '', expectedQuantity: 0, actualQuantity: 0 })}>
              {t('receiving.addItem')}
            </Button>
          </div>
        )}
      />
      
      <Button
        type="submit"
        variant="contained"
        disabled={isLoading}
        fullWidth
      >
        {isLoading ? <CircularProgress size={24} /> : t('receiving.submit')}
      </Button>
    </form>
  );
};
```
```

## Best Practices

1. **Provide Specific Context**
   - Explicitly reference related files and components
   - Show existing patterns and approaches

2. **Emphasize Security Requirements**
   - Always include authentication and authorization verification
   - Reference proper access control implementation

3. **Specify Error Handling and Security**
   - Clearly state expected error handling patterns
   - Clearly state security requirements

4. **Provide Reference Code**
   - Include examples from similar existing implementations
   - Show project-specific patterns

5. **Define Clear Deliverables**
   - Specify expected output format and structure
   - Include acceptance criteria
