# Smart Factory WMS - AIプロンプトテンプレート

## 目的

このテンプレートは、Smart Factory WMSプロジェクトでAIコーディングアシスタントを使用する際の標準化されたアプローチを提供します。これにより、一貫したコード品質、セキュリティ対応、およびプロジェクト標準への準拠を確保します。

## 基本テンプレート構造

```
# Smart Factory WMS - [タスクの簡単な説明]

## プロジェクトコンテキスト
- Smart Factory WMSシステム
- [関連するアーキテクチャコンポーネント]
- [関連する既存パターン/ファイルへの参照]

## タスク要件
[具体的なタスクの説明]

## 技術要件
- 適切な認証・認可の実装
- プロジェクトのコーディング標準に従う
- 適切なエラー処理を実装
- [その他の技術要件]

## 参照コード例
[関連する既存コードの例]
```

## 使用例

### バックエンドサービスメソッド

```
# Smart Factory WMS - 新しい入荷サービスメソッド

## プロジェクトコンテキスト
- Smart Factory WMSシステム
- 入荷管理モジュールの一部
- サービスレイヤーパターンに従う

## タスク要件
入荷予定に基づいて新しい入荷を登録するサービスメソッドを作成してください。
入荷IDを生成し、関連する在庫レコードを作成する必要があります。

## 技術要件
- 適切な認証・認可の実装
- トランザクション管理を実装
- 適切な例外処理を含める
- ロギングを実装
- 入力検証を行う

## 参照コード例
```python
async def create_shipment(self, shipment_data: ShipmentCreate, current_user: User) -> Shipment:
    # 認証・認可チェック
    if not current_user.has_permission('shipment:create'):
        raise PermissionError("Insufficient permissions to create shipment")

    async with self.db.transaction():
        try:
            # 処理ロジック
            # ...

            return created_shipment
        except Exception as e:
            self.logger.error(f"Shipment creation failed: {str(e)}")
            raise ServiceError(f"Failed to create shipment: {str(e)}")
```

### フロントエンドコンポーネント

```
# Smart Factory WMS - 在庫一覧表示コンポーネント

## プロジェクトコンテキスト
- React + TypeScriptベースのフロントエンド
- レスポンシブなUI設計
- Material-UIコンポーネントライブラリ使用
- 在庫管理モジュールの一部

## タスク要件
在庫アイテムを一覧表示するReactコンポーネントを作成してください。
データを表示し、検索・フィルタリング機能を含める必要があります。

## 技術要件
- TypeScriptの型安全性を確保
- 適切な認証状態の処理
- レスポンシブデザインの実装
- エラー状態とローディング状態の処理
- アクセシビリティ対応
- 国際化（i18n）対応

## 参照コード例
```typescript
interface InventoryListProps {
  onItemSelect?: (item: InventoryItem) => void;
  filters?: InventoryFilters;
}

const InventoryList: React.FC<InventoryListProps> = ({ onItemSelect, filters }) => {
  const { data, loading, error } = useInventoryQuery(filters);
  const { t } = useTranslation();
  const { user } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!user?.hasPermission('inventory:read')) {
    return <AccessDeniedMessage />;
  }

  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('inventory.itemCode')}</TableCell>
            <TableCell>{t('inventory.itemName')}</TableCell>
            <TableCell>{t('inventory.quantity')}</TableCell>
            <TableCell>{t('inventory.location')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data?.items.map((item) => (
            <TableRow key={item.id} onClick={() => onItemSelect?.(item)}>
              <TableCell>{item.code}</TableCell>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.quantity}</TableCell>
              <TableCell>{item.location}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
```
```

### フロントエンドフォーム

```
# Smart Factory WMS - 入荷登録フォーム

## プロジェクトコンテキスト
- React Hook Formを使用したフォーム管理
- Zodによるバリデーション
- セキュアなフォーム処理
- 入荷管理モジュールの一部

## タスク要件
新しい入荷を登録するためのフォームコンポーネントを作成してください。
入力検証、エラー表示、送信処理を含める必要があります。

## 技術要件
- React Hook Formによるフォーム状態管理
- Zodスキーマによる入力検証
- 適切な認証・認可の確認
- 楽観的UI更新
- エラーハンドリングとユーザーフィードバック
- アクセシビリティ対応

## 参照コード例
```typescript
const receivingSchema = z.object({
  planId: z.string().min(1, 'Plan ID is required'),
  items: z.array(z.object({
    itemCode: z.string().min(1, 'Item code is required'),
    expectedQuantity: z.number().min(1, 'Quantity must be positive'),
    actualQuantity: z.number().min(1, 'Actual quantity must be positive'),
  })).min(1, 'At least one item is required'),
});

type ReceivingFormData = z.infer<typeof receivingSchema>;

const ReceivingForm: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { mutate: submitReceiving, isLoading } = useReceivingMutation();

  const form = useForm<ReceivingFormData>({
    resolver: zodResolver(receivingSchema),
    defaultValues: {
      planId: '',
      items: [{ itemCode: '', expectedQuantity: 0, actualQuantity: 0 }],
    },
  });

  const onSubmit = (data: ReceivingFormData) => {
    if (!user?.hasPermission('receiving:create')) {
      toast.error(t('receiving.permissionDenied'));
      return;
    }

    submitReceiving(
      data,
      {
        onSuccess: () => {
          toast.success(t('receiving.submitSuccess'));
          form.reset();
        },
        onError: (error) => {
          toast.error(t('receiving.submitError', { error: error.message }));
        },
      }
    );
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <TextField
        {...form.register('planId')}
        label={t('receiving.planId')}
        error={!!form.formState.errors.planId}
        helperText={form.formState.errors.planId?.message}
        fullWidth
        margin="normal"
      />

      {/* 動的アイテムリスト */}
      <FieldArray
        control={form.control}
        name="items"
        render={({ fields, append, remove }) => (
          <div>
            {fields.map((field, index) => (
              <div key={field.id}>
                <TextField
                  {...form.register(`items.${index}.itemCode`)}
                  label={t('receiving.itemCode')}
                  error={!!form.formState.errors.items?.[index]?.itemCode}
                  helperText={form.formState.errors.items?.[index]?.itemCode?.message}
                />
                {/* 他のフィールド */}
              </div>
            ))}
            <Button onClick={() => append({ itemCode: '', expectedQuantity: 0, actualQuantity: 0 })}>
              {t('receiving.addItem')}
            </Button>
          </div>
        )}
      />

      <Button
        type="submit"
        variant="contained"
        disabled={isLoading}
        fullWidth
      >
        {isLoading ? <CircularProgress size={24} /> : t('receiving.submit')}
      </Button>
    </form>
  );
};
```
```

## ベストプラクティス

1. **具体的なコンテキストを提供する**
   - 関連するファイルやコンポーネントを明示的に参照
   - 既存のパターンやアプローチを示す

2. **セキュリティ要件を強調する**
   - 認証・認可の確認を常に含める
   - 適切なアクセス制御の実装を参照

3. **エラー処理とセキュリティを指定する**
   - 期待されるエラー処理パターンを明示
   - セキュリティ要件を明確に述べる

4. **参照コードを提供する**
   - 類似の既存実装からの例を含める
   - プロジェクト固有のパターンを示す

5. **明確な成果物を定義する**
   - 期待される出力の形式と構造を指定
   - 受け入れ基準を含める