name: Smart Factory WMS CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  FLUTTER_VERSION: '3.16.0'

jobs:
  # Backend Testing
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: smart_factory_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements/*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements/dev.txt
    
    - name: Run linting
      run: |
        cd backend
        flake8 app/
        black --check app/
        mypy app/
    
    - name: Run security checks
      run: |
        cd backend
        bandit -r app/
    
    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/smart_factory_test
        REDIS_URL: redis://localhost:6379/0
        SECRET_KEY: test-secret-key
      run: |
        cd backend
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Frontend Web Testing
  frontend-web-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend-web/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend-web
        npm ci
    
    - name: Run linting
      run: |
        cd frontend-web
        npm run lint
    
    - name: Run type checking
      run: |
        cd frontend-web
        npm run type-check
    
    - name: Run unit tests
      run: |
        cd frontend-web
        npm run test:unit
    
    - name: Build application
      run: |
        cd frontend-web
        npm run build
    
    - name: Run E2E tests
      run: |
        cd frontend-web
        npm run test:e2e:headless

  # Frontend Mobile Testing
  frontend-mobile-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
    
    - name: Get dependencies
      run: |
        cd frontend-mobile
        flutter pub get
    
    - name: Run analyzer
      run: |
        cd frontend-mobile
        flutter analyze
    
    - name: Run tests
      run: |
        cd frontend-mobile
        flutter test --coverage
    
    - name: Build APK
      run: |
        cd frontend-mobile
        flutter build apk --debug

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Docker Build
  docker-build:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-web-test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ secrets.CONTAINER_REGISTRY }}
        username: ${{ secrets.REGISTRY_USERNAME }}
        password: ${{ secrets.REGISTRY_PASSWORD }}
    
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          ${{ secrets.CONTAINER_REGISTRY }}/smart-factory-wms/backend:${{ github.sha }}
          ${{ secrets.CONTAINER_REGISTRY }}/smart-factory-wms/backend:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend-web
        push: true
        tags: |
          ${{ secrets.CONTAINER_REGISTRY }}/smart-factory-wms/frontend:${{ github.sha }}
          ${{ secrets.CONTAINER_REGISTRY }}/smart-factory-wms/frontend:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deployment (placeholder for future implementation)
  deploy:
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to staging
      run: |
        echo "Deployment to staging environment"
        # Deployment logic will be implemented here
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests"
        # Smoke test logic will be implemented here
