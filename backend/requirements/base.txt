# ============================================================================
# Smart Factory WMS - Backend Base Requirements
# ============================================================================

# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# Data Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# Utilities
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0
structlog==23.2.0

# File Processing
openpyxl==3.1.2
pandas==2.1.4
pillow==10.1.0

# Background Tasks
celery==5.3.4
flower==2.0.1

# Monitoring & Logging
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# AWS SDK
boto3==1.34.0
botocore==1.34.0

# Configuration
dynaconf==3.2.4
