# ============================================================================
# Smart Factory WMS - Backend Development Requirements
# ============================================================================

# Include base requirements
-r base.txt

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
httpx==0.25.2
factory-boy==3.3.0
faker==20.1.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-bugbear==23.11.28
mypy==1.7.1
bandit==1.7.5
safety==2.3.5

# Development Tools
pre-commit==3.6.0
ipython==8.17.2
ipdb==0.13.13
watchdog==3.0.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
sphinx-autodoc-typehints==1.25.2

# Database Tools
pgcli==4.0.1
alembic-utils==0.8.2

# Performance Profiling
py-spy==0.3.14
memory-profiler==0.61.0

# API Documentation
redoc==2.1.0

# Development Server
reload==1.0.0
