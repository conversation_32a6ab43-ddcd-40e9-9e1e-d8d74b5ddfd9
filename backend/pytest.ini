[tool:pytest]
# Smart Factory WMS - Pytest Configuration

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --asyncio-mode=auto

# Markers
markers =
    asyncio: mark test as async
    unit: mark test as unit test
    integration: mark test as integration test
    slow: mark test as slow running
    auth: mark test as authentication related
    api: mark test as API related
    database: mark test as database related
    security: mark test as security related

# Async settings
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# Test timeout (in seconds)
timeout = 300

# Coverage settings
[coverage:run]
source = app
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
