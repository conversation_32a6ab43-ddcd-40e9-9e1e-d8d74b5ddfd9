"""
Smart Factory WMS - Rate Limiting Service

This service handles rate limiting for various operations including
authentication attempts to prevent brute force attacks.
"""

import time
from typing import Dict, Optional
from datetime import datetime, timedelta
import structlog

from app.core.config import settings
from app.core.exceptions import RateLimitException

logger = structlog.get_logger()


class RateLimitService:
    """Service for handling rate limiting operations."""
    
    def __init__(self):
        # In-memory storage for rate limiting
        # In production, this should use Redis or another persistent store
        self._attempts: Dict[str, Dict] = {}
        self._cleanup_interval = 300  # Clean up old entries every 5 minutes
        self._last_cleanup = time.time()
    
    def _cleanup_old_entries(self) -> None:
        """Clean up old rate limiting entries."""
        current_time = time.time()
        
        # Only cleanup if enough time has passed
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        cutoff_time = current_time - settings.RATE_LIMIT_PERIOD
        keys_to_remove = []
        
        for key, data in self._attempts.items():
            # Remove entries older than the rate limit period
            data['attempts'] = [
                attempt_time for attempt_time in data['attempts']
                if attempt_time > cutoff_time
            ]
            
            # If no recent attempts, mark for removal
            if not data['attempts']:
                keys_to_remove.append(key)
        
        # Remove empty entries
        for key in keys_to_remove:
            del self._attempts[key]
        
        self._last_cleanup = current_time
    
    def _get_rate_limit_key(self, identifier: str, operation: str) -> str:
        """Generate rate limit key for identifier and operation."""
        return f"{operation}:{identifier}"
    
    def check_rate_limit(
        self, 
        identifier: str, 
        operation: str = "login",
        max_attempts: Optional[int] = None,
        period: Optional[int] = None
    ) -> None:
        """
        Check if rate limit is exceeded for the given identifier and operation.
        
        Args:
            identifier: Unique identifier (IP address, username, etc.)
            operation: Operation type (login, api_call, etc.)
            max_attempts: Maximum attempts allowed (defaults to config)
            period: Time period in seconds (defaults to config)
            
        Raises:
            RateLimitException: If rate limit is exceeded
        """
        # Clean up old entries periodically
        self._cleanup_old_entries()
        
        # Use config defaults if not specified
        max_attempts = max_attempts or settings.RATE_LIMIT_REQUESTS
        period = period or settings.RATE_LIMIT_PERIOD
        
        key = self._get_rate_limit_key(identifier, operation)
        current_time = time.time()
        cutoff_time = current_time - period
        
        # Get or create entry for this key
        if key not in self._attempts:
            self._attempts[key] = {
                'attempts': [],
                'first_attempt': current_time,
                'blocked_until': None
            }
        
        entry = self._attempts[key]
        
        # Check if currently blocked
        if entry['blocked_until'] and current_time < entry['blocked_until']:
            remaining_time = int(entry['blocked_until'] - current_time)
            raise RateLimitException(
                f"Too many {operation} attempts. Try again in {remaining_time} seconds.",
                details={
                    "operation": operation,
                    "identifier": identifier,
                    "retry_after": remaining_time
                }
            )
        
        # Remove old attempts outside the time window
        entry['attempts'] = [
            attempt_time for attempt_time in entry['attempts']
            if attempt_time > cutoff_time
        ]
        
        # Check if limit exceeded
        if len(entry['attempts']) >= max_attempts:
            # Block for the remaining period
            entry['blocked_until'] = current_time + period
            
            logger.warning(
                "Rate limit exceeded",
                identifier=identifier,
                operation=operation,
                attempts=len(entry['attempts']),
                max_attempts=max_attempts,
                period=period
            )
            
            raise RateLimitException(
                f"Too many {operation} attempts. Try again in {period} seconds.",
                details={
                    "operation": operation,
                    "identifier": identifier,
                    "retry_after": period,
                    "attempts": len(entry['attempts']),
                    "max_attempts": max_attempts
                }
            )
    
    def record_attempt(
        self, 
        identifier: str, 
        operation: str = "login",
        success: bool = False
    ) -> None:
        """
        Record an attempt for rate limiting.
        
        Args:
            identifier: Unique identifier (IP address, username, etc.)
            operation: Operation type (login, api_call, etc.)
            success: Whether the attempt was successful
        """
        key = self._get_rate_limit_key(identifier, operation)
        current_time = time.time()
        
        # Get or create entry for this key
        if key not in self._attempts:
            self._attempts[key] = {
                'attempts': [],
                'first_attempt': current_time,
                'blocked_until': None
            }
        
        entry = self._attempts[key]
        
        # Record the attempt
        entry['attempts'].append(current_time)
        
        # If successful, we might want to reset or reduce the count
        if success:
            # For successful login, we can be more lenient
            # Keep only the last few attempts to allow for normal usage
            entry['attempts'] = entry['attempts'][-5:]  # Keep last 5 attempts
            entry['blocked_until'] = None  # Clear any block
        
        logger.debug(
            "Rate limit attempt recorded",
            identifier=identifier,
            operation=operation,
            success=success,
            total_attempts=len(entry['attempts'])
        )
    
    def reset_rate_limit(self, identifier: str, operation: str = "login") -> None:
        """
        Reset rate limit for the given identifier and operation.

        Args:
            identifier: Unique identifier (IP address, username, etc.)
            operation: Operation type (login, api_call, etc.)
        """
        key = self._get_rate_limit_key(identifier, operation)

        if key in self._attempts:
            del self._attempts[key]

        logger.info(
            "Rate limit reset",
            identifier=identifier,
            operation=operation
        )

    def reset_all_rate_limits(self) -> None:
        """
        Reset all rate limits. Useful for testing.
        """
        self._attempts.clear()
        logger.info("All rate limits reset")
    
    def get_rate_limit_status(
        self, 
        identifier: str, 
        operation: str = "login",
        max_attempts: Optional[int] = None,
        period: Optional[int] = None
    ) -> Dict:
        """
        Get current rate limit status for identifier and operation.
        
        Args:
            identifier: Unique identifier (IP address, username, etc.)
            operation: Operation type (login, api_call, etc.)
            max_attempts: Maximum attempts allowed (defaults to config)
            period: Time period in seconds (defaults to config)
            
        Returns:
            Dict with rate limit status information
        """
        # Use config defaults if not specified
        max_attempts = max_attempts or settings.RATE_LIMIT_REQUESTS
        period = period or settings.RATE_LIMIT_PERIOD
        
        key = self._get_rate_limit_key(identifier, operation)
        current_time = time.time()
        cutoff_time = current_time - period
        
        if key not in self._attempts:
            return {
                "attempts": 0,
                "max_attempts": max_attempts,
                "remaining": max_attempts,
                "reset_time": None,
                "blocked": False
            }
        
        entry = self._attempts[key]
        
        # Count recent attempts
        recent_attempts = [
            attempt_time for attempt_time in entry['attempts']
            if attempt_time > cutoff_time
        ]
        
        # Check if blocked
        blocked = entry['blocked_until'] and current_time < entry['blocked_until']
        reset_time = entry['blocked_until'] if blocked else None
        
        return {
            "attempts": len(recent_attempts),
            "max_attempts": max_attempts,
            "remaining": max(0, max_attempts - len(recent_attempts)),
            "reset_time": reset_time,
            "blocked": blocked
        }


# Global rate limit service instance
rate_limit_service = RateLimitService()
