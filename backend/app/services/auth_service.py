"""
Smart Factory WMS - Authentication Service

This service handles authentication-related business logic including
user authentication, session management, and password operations.
"""

from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
import secrets
import structlog

from app.core.config import settings
from app.core.security import (
    verify_password, get_password_hash, create_access_token, 
    create_refresh_token, verify_token, generate_password_reset_token
)
from app.core.exceptions import (
    AuthenticationException, ResourceNotFoundException,
    InvalidOperationException, AccountLockedException,
    UserNotFoundException, RateLimitException
)
from app.models.user import User, UserSession
from app.services.user_service import UserService
from app.services.rate_limit_service import rate_limit_service

logger = structlog.get_logger()


class AuthService:
    """Service for handling authentication operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.user_service = UserService(db)
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        client_ip: str = None,
        user_agent: str = None,
        remember_me: bool = False
    ) -> Dict[str, Any]:
        """
        Authenticate user with username/email and password.

        Args:
            username: Username or email address
            password: User password
            client_ip: Client IP address
            user_agent: Client user agent
            remember_me: Whether to create long-lived session

        Returns:
            Dict containing tokens and user information

        Raises:
            AuthenticationException: If authentication fails
        """
        # Check rate limiting for this IP and username
        if client_ip:
            rate_limit_service.check_rate_limit(client_ip, "login_ip", max_attempts=10, period=300)  # 10 attempts per 5 minutes per IP
        rate_limit_service.check_rate_limit(username, "login_user", max_attempts=5, period=300)  # 5 attempts per 5 minutes per user

        # Get user by username or email
        user = await self._get_user_by_username_or_email(username)
        if not user:
            # Record failed attempt
            if client_ip:
                rate_limit_service.record_attempt(client_ip, "login_ip", success=False)
            rate_limit_service.record_attempt(username, "login_user", success=False)
            await self._handle_failed_login(username, "User not found", client_ip)
            raise UserNotFoundException("Invalid username or password")

        # Check if user is active
        if not user.is_active:
            # Record failed attempt
            if client_ip:
                rate_limit_service.record_attempt(client_ip, "login_ip", success=False)
            rate_limit_service.record_attempt(username, "login_user", success=False)
            await self._handle_failed_login(email, "User inactive", client_ip)
            raise AuthenticationException("Account is disabled")

        # Check if user is locked
        if user.is_locked:
            # Record failed attempt
            if client_ip:
                rate_limit_service.record_attempt(client_ip, "login_ip", success=False)
            rate_limit_service.record_attempt(username, "login_user", success=False)
            await self._handle_failed_login(username, "User locked", client_ip)
            raise AccountLockedException("Account is locked. Please contact administrator.")

        # Verify password
        if not verify_password(password, user.hashed_password):
            # Record failed attempt
            if client_ip:
                rate_limit_service.record_attempt(client_ip, "login_ip", success=False)
            rate_limit_service.record_attempt(username, "login_user", success=False)
            await self._handle_failed_login(username, "Invalid password", client_ip, user.id)
            raise AuthenticationException("Invalid username or password")
        
        # Record successful attempt
        if client_ip:
            rate_limit_service.record_attempt(client_ip, "login_ip", success=True)
        rate_limit_service.record_attempt(username, "login_user", success=True)

        # Reset failed login attempts on successful login
        if user.failed_login_attempts > 0:
            await self._reset_failed_login_attempts(user.id)
        
        # Create tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        refresh_token_expires = timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS * (7 if remember_me else 1)
        )
        
        access_token = create_access_token(
            subject=str(user.id),
            expires_delta=access_token_expires
        )
        refresh_token = create_refresh_token(
            subject=str(user.id),
            expires_delta=refresh_token_expires
        )
        
        # Create user session
        session = await self._create_user_session(
            user_id=user.id,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + refresh_token_expires,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        # Update last login
        await self._update_last_login(user.id, client_ip)
        
        # Load user with relationships
        user_with_roles = await self.user_service.get_user_with_roles(user.id)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": int(access_token_expires.total_seconds()),
            "user": user_with_roles,
            "session_id": session.id
        }
    
    async def refresh_user_token(
        self,
        user: User,
        refresh_token: str
    ) -> Dict[str, Any]:
        """
        Refresh user access token.
        
        Args:
            user: User object
            refresh_token: Current refresh token
            
        Returns:
            Dict containing new tokens
            
        Raises:
            AuthenticationException: If refresh fails
        """
        # Verify refresh token
        user_id = verify_token(refresh_token, token_type="refresh")
        if not user_id or int(user_id) != user.id:
            raise AuthenticationException("Invalid refresh token")
        
        # Check if session exists and is active
        session = await self._get_active_session_by_refresh_token(refresh_token)
        if not session:
            raise AuthenticationException("Session not found or expired")
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=str(user.id),
            expires_delta=access_token_expires
        )
        
        # Optionally rotate refresh token (recommended for security)
        new_refresh_token = None
        if settings.ENVIRONMENT == "production":
            refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
            new_refresh_token = create_refresh_token(
                subject=str(user.id),
                expires_delta=refresh_token_expires
            )
            
            # Update session with new refresh token
            await self._update_session_refresh_token(session.id, new_refresh_token)
        
        # Update session activity
        await self._update_session_activity(session.id)
        
        result = {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": int(access_token_expires.total_seconds())
        }
        
        if new_refresh_token:
            result["refresh_token"] = new_refresh_token
        
        return result
    
    async def logout_user(self, user_id: int, access_token: str = None) -> None:
        """
        Logout user and invalidate session.
        
        Args:
            user_id: User ID
            access_token: Access token to invalidate
        """
        # End all active sessions for user
        await self._end_user_sessions(user_id)
        
        # TODO: Add token to blacklist if using token blacklisting
        # This would require a token blacklist table or Redis cache
        
        logger.info("User logged out", user_id=user_id)
    
    async def initiate_password_reset(self, email: str) -> None:
        """
        Initiate password reset process.
        
        Args:
            email: User email
        """
        user = await self.user_service.get_user_by_email(email)
        if not user or not user.is_active:
            # Don't reveal if user exists for security
            logger.warning("Password reset attempted for non-existent user", email=email)
            return
        
        # Generate reset token
        reset_token = generate_password_reset_token()
        expires_at = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
        
        # Store reset token (you might want to create a password_reset_tokens table)
        # For now, we'll use a simple approach with user table
        await self.user_service.set_password_reset_token(user.id, reset_token, expires_at)
        
        # TODO: Send email with reset link
        # This would integrate with an email service
        
        logger.info("Password reset initiated", user_id=user.id, email=email)
    
    async def reset_password(self, token: str, new_password: str) -> None:
        """
        Reset user password using reset token.
        
        Args:
            token: Password reset token
            new_password: New password
            
        Raises:
            AuthenticationException: If token is invalid or expired
        """
        # Find user by reset token
        user = await self.user_service.get_user_by_reset_token(token)
        if not user:
            raise AuthenticationException("Invalid or expired reset token")
        
        # Update password
        await self.user_service.update_user_password(user.id, new_password)
        
        # Clear reset token
        await self.user_service.clear_password_reset_token(user.id)
        
        # End all active sessions
        await self._end_user_sessions(user.id)
        
        logger.info("Password reset completed", user_id=user.id)
    
    async def _handle_failed_login(
        self,
        email: str,
        reason: str,
        client_ip: str = None,
        user_id: int = None
    ) -> None:
        """Handle failed login attempt."""
        logger.warning(
            "Failed login attempt",
            email=email,
            reason=reason,
            client_ip=client_ip,
            user_id=user_id
        )
        
        if user_id:
            # Increment failed login attempts
            await self._increment_failed_login_attempts(user_id)
    
    async def _increment_failed_login_attempts(self, user_id: int) -> None:
        """Increment failed login attempts and lock account if necessary."""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                failed_login_attempts=User.failed_login_attempts + 1,
                updated_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
        
        # Check if account should be locked
        user = await self.user_service.get_user_by_id(user_id)
        if user and user.failed_login_attempts >= 5:  # Lock after 5 failed attempts
            lock_until = datetime.utcnow() + timedelta(minutes=30)  # Lock for 30 minutes
            await self._lock_user_account(user_id, lock_until)
    
    async def _reset_failed_login_attempts(self, user_id: int) -> None:
        """Reset failed login attempts."""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                failed_login_attempts=0,
                locked_until=None,
                updated_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
    
    async def _lock_user_account(self, user_id: int, lock_until: datetime) -> None:
        """Lock user account until specified time."""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                locked_until=lock_until,
                updated_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
        
        logger.warning("User account locked", user_id=user_id, lock_until=lock_until)
    
    async def _update_last_login(self, user_id: int, client_ip: str = None) -> None:
        """Update user's last login information."""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                last_login_at=datetime.utcnow(),
                last_login_ip=client_ip,
                updated_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
    
    async def _create_user_session(
        self,
        user_id: int,
        access_token: str,
        refresh_token: str,
        expires_at: datetime,
        client_ip: str = None,
        user_agent: str = None
    ) -> UserSession:
        """Create new user session."""
        session = UserSession(
            user_id=user_id,
            session_token=access_token,
            refresh_token=refresh_token,
            expires_at=expires_at,
            ip_address=client_ip,
            user_agent=user_agent,
            device_type="web",  # Could be determined from user_agent
            is_active=True
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        
        return session
    
    async def _get_active_session_by_refresh_token(self, refresh_token: str) -> Optional[UserSession]:
        """Get active session by refresh token."""
        stmt = select(UserSession).where(
            and_(
                UserSession.refresh_token == refresh_token,
                UserSession.is_active == True,
                UserSession.expires_at > datetime.utcnow()
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def _update_session_refresh_token(self, session_id: int, new_refresh_token: str) -> None:
        """Update session refresh token."""
        stmt = (
            update(UserSession)
            .where(UserSession.id == session_id)
            .values(
                refresh_token=new_refresh_token,
                last_activity_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
    
    async def _update_session_activity(self, session_id: int) -> None:
        """Update session last activity."""
        stmt = (
            update(UserSession)
            .where(UserSession.id == session_id)
            .values(last_activity_at=datetime.utcnow())
        )
        await self.db.execute(stmt)
    
    async def _end_user_sessions(self, user_id: int) -> None:
        """End all active sessions for user."""
        stmt = (
            update(UserSession)
            .where(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active == True
                )
            )
            .values(
                is_active=False,
                ended_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
        await self.db.commit()

    async def _get_user_by_username_or_email(self, username_or_email: str) -> Optional[User]:
        """
        Get user by username or email address.

        Args:
            username_or_email: Username or email address

        Returns:
            User object or None if not found
        """
        import re

        # Check if it looks like an email
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        if re.match(email_pattern, username_or_email):
            # Try to get user by email
            return await self.user_service.get_user_by_email(username_or_email)
        else:
            # Try to get user by username
            return await self.user_service.get_user_by_username(username_or_email)
