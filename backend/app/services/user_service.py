"""
Smart Factory WMS - User Service

This service handles user-related business logic including user management,
role assignment, and permission checking.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload
import structlog

from app.core.security import get_password_hash
from app.core.exceptions import (
    ResourceNotFoundException, DuplicateResourceException,
    ValidationException, InvalidOperationException
)
from app.models.user import User, Role, Permission, UserSession

logger = structlog.get_logger()


class UserService:
    """Service for handling user operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            User object or None if not found
        """
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email.
        
        Args:
            email: User email
            
        Returns:
            User object or None if not found
        """
        stmt = select(User).where(User.email == email)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """
        Get user by username.
        
        Args:
            username: Username
            
        Returns:
            User object or None if not found
        """
        stmt = select(User).where(User.username == username)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_with_roles(self, user_id: int) -> Optional[User]:
        """
        Get user with roles and permissions.
        
        Args:
            user_id: User ID
            
        Returns:
            User object with roles loaded or None if not found
        """
        stmt = (
            select(User)
            .options(
                selectinload(User.roles).selectinload(Role.permissions)
            )
            .where(User.id == user_id)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_users(
        self,
        skip: int = 0,
        limit: int = 100,
        search: str = None,
        is_active: bool = None,
        role_id: int = None
    ) -> List[User]:
        """
        Get list of users with optional filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Search term for name, email, or username
            is_active: Filter by active status
            role_id: Filter by role ID
            
        Returns:
            List of User objects
        """
        stmt = select(User).options(selectinload(User.roles))
        
        # Apply filters
        conditions = []
        
        if search:
            search_term = f"%{search}%"
            conditions.append(
                or_(
                    User.first_name.ilike(search_term),
                    User.last_name.ilike(search_term),
                    User.email.ilike(search_term),
                    User.username.ilike(search_term)
                )
            )
        
        if is_active is not None:
            conditions.append(User.is_active == is_active)
        
        if role_id:
            # Join with user_roles table to filter by role
            stmt = stmt.join(User.roles).where(Role.id == role_id)
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        stmt = stmt.offset(skip).limit(limit).order_by(User.created_at.desc())
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """
        Create new user.
        
        Args:
            user_data: User data dictionary
            
        Returns:
            Created User object
            
        Raises:
            DuplicateResourceException: If email or username already exists
        """
        # Check for existing email
        existing_user = await self.get_user_by_email(user_data["email"])
        if existing_user:
            raise DuplicateResourceException("User", user_data["email"])
        
        # Check for existing username
        existing_user = await self.get_user_by_username(user_data["username"])
        if existing_user:
            raise DuplicateResourceException("User", user_data["username"])
        
        # Create user
        user = User(**user_data)
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User created", user_id=user.id, email=user.email)
        return user
    
    async def update_user(self, user_id: int, user_data: Dict[str, Any]) -> User:
        """
        Update user information.
        
        Args:
            user_id: User ID
            user_data: Updated user data
            
        Returns:
            Updated User object
            
        Raises:
            ResourceNotFoundException: If user not found
            DuplicateResourceException: If email or username conflicts
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            raise ResourceNotFoundException("User", str(user_id))
        
        # Check for email conflicts
        if "email" in user_data and user_data["email"] != user.email:
            existing_user = await self.get_user_by_email(user_data["email"])
            if existing_user and existing_user.id != user_id:
                raise DuplicateResourceException("User", user_data["email"])
        
        # Check for username conflicts
        if "username" in user_data and user_data["username"] != user.username:
            existing_user = await self.get_user_by_username(user_data["username"])
            if existing_user and existing_user.id != user_id:
                raise DuplicateResourceException("User", user_data["username"])
        
        # Update user
        for key, value in user_data.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        user.updated_at = datetime.utcnow()
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User updated", user_id=user.id)
        return user
    
    async def update_user_password(self, user_id: int, new_password: str) -> None:
        """
        Update user password.
        
        Args:
            user_id: User ID
            new_password: New password (plain text)
            
        Raises:
            ResourceNotFoundException: If user not found
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            raise ResourceNotFoundException("User", str(user_id))
        
        # Hash new password
        hashed_password = get_password_hash(new_password)
        
        # Update password
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                hashed_password=hashed_password,
                password_changed_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
        await self.db.commit()
        
        logger.info("User password updated", user_id=user_id)
    
    async def delete_user(self, user_id: int) -> None:
        """
        Delete user (soft delete by deactivating).
        
        Args:
            user_id: User ID
            
        Raises:
            ResourceNotFoundException: If user not found
            InvalidOperationException: If trying to delete superuser
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            raise ResourceNotFoundException("User", str(user_id))
        
        if user.is_superuser:
            raise InvalidOperationException("delete_user", "Cannot delete superuser")
        
        # Soft delete by deactivating
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                is_active=False,
                updated_at=datetime.utcnow()
            )
        )
        await self.db.execute(stmt)
        await self.db.commit()
        
        logger.info("User deleted (deactivated)", user_id=user_id)
    
    async def assign_role_to_user(self, user_id: int, role_id: int) -> None:
        """
        Assign role to user.
        
        Args:
            user_id: User ID
            role_id: Role ID
            
        Raises:
            ResourceNotFoundException: If user or role not found
        """
        user = await self.get_user_with_roles(user_id)
        if not user:
            raise ResourceNotFoundException("User", str(user_id))
        
        role = await self.get_role_by_id(role_id)
        if not role:
            raise ResourceNotFoundException("Role", str(role_id))
        
        # Check if user already has this role
        if role in user.roles:
            return  # Already assigned
        
        # Assign role
        user.roles.append(role)
        await self.db.commit()
        
        logger.info("Role assigned to user", user_id=user_id, role_id=role_id)
    
    async def remove_role_from_user(self, user_id: int, role_id: int) -> None:
        """
        Remove role from user.
        
        Args:
            user_id: User ID
            role_id: Role ID
            
        Raises:
            ResourceNotFoundException: If user or role not found
        """
        user = await self.get_user_with_roles(user_id)
        if not user:
            raise ResourceNotFoundException("User", str(user_id))
        
        role = await self.get_role_by_id(role_id)
        if not role:
            raise ResourceNotFoundException("Role", str(role_id))
        
        # Remove role if assigned
        if role in user.roles:
            user.roles.remove(role)
            await self.db.commit()
            
            logger.info("Role removed from user", user_id=user_id, role_id=role_id)
    
    async def get_role_by_id(self, role_id: int) -> Optional[Role]:
        """Get role by ID."""
        stmt = select(Role).where(Role.id == role_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_by_reset_token(self, token: str) -> Optional[User]:
        """
        Get user by password reset token.
        
        Note: This is a simplified implementation. In production,
        you should use a separate password_reset_tokens table.
        """
        # This would need to be implemented with a proper reset token table
        # For now, returning None as placeholder
        return None
    
    async def set_password_reset_token(
        self, 
        user_id: int, 
        token: str, 
        expires_at: datetime
    ) -> None:
        """
        Set password reset token for user.
        
        Note: This is a simplified implementation. In production,
        you should use a separate password_reset_tokens table.
        """
        # This would need to be implemented with a proper reset token table
        pass
    
    async def clear_password_reset_token(self, user_id: int) -> None:
        """
        Clear password reset token for user.
        
        Note: This is a simplified implementation. In production,
        you should use a separate password_reset_tokens table.
        """
        # This would need to be implemented with a proper reset token table
        pass
    
    async def get_user_sessions(self, user_id: int) -> List[UserSession]:
        """
        Get active sessions for user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of active UserSession objects
        """
        stmt = (
            select(UserSession)
            .where(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.utcnow()
                )
            )
            .order_by(UserSession.last_activity_at.desc())
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def revoke_user_session(self, user_id: int, session_id: int) -> None:
        """
        Revoke specific user session.
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Raises:
            ResourceNotFoundException: If session not found
        """
        stmt = (
            update(UserSession)
            .where(
                and_(
                    UserSession.id == session_id,
                    UserSession.user_id == user_id,
                    UserSession.is_active == True
                )
            )
            .values(
                is_active=False,
                ended_at=datetime.utcnow()
            )
        )
        result = await self.db.execute(stmt)
        
        if result.rowcount == 0:
            raise ResourceNotFoundException("Session", str(session_id))
        
        await self.db.commit()
        logger.info("User session revoked", user_id=user_id, session_id=session_id)
