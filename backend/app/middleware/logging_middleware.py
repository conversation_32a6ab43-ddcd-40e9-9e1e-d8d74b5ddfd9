"""
Smart Factory WMS - Logging Middleware

This middleware handles request/response logging, performance monitoring,
and audit trail generation.
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

from app.core.config import settings

logger = structlog.get_logger()


class LoggingMiddleware(BaseHTTPMiddleware):
    """Logging middleware for request/response tracking and performance monitoring."""
    
    # Sensitive headers that should not be logged
    SENSITIVE_HEADERS = {
        "authorization",
        "cookie",
        "x-api-key",
        "x-auth-token",
    }
    
    # Endpoints to exclude from detailed logging (health checks, etc.)
    EXCLUDE_PATHS = {
        "/health",
        "/metrics",
    }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        
        # Get or generate request ID for tracing
        request_id = request.headers.get("X-Request-ID") or str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Record start time
        start_time = time.time()
        
        # Skip detailed logging for excluded paths
        should_log_details = request.url.path not in self.EXCLUDE_PATHS
        
        if should_log_details:
            # Log incoming request
            await self._log_request(request, request_id)
        
        # Process request
        try:
            response = await call_next(request)
        except Exception as e:
            # Log exception
            processing_time = time.time() - start_time
            logger.error(
                "Request processing failed",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                processing_time=processing_time,
                error=str(e),
                exc_info=True,
            )
            raise
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Add headers to response
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Processing-Time"] = str(processing_time)
        
        if should_log_details:
            # Log response
            await self._log_response(request, response, request_id, processing_time)
        
        return response
    
    async def _log_request(self, request: Request, request_id: str):
        """Log incoming request details."""
        
        # Get client information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Get user ID if available
        user_id = getattr(request.state, "user_id", None)
        
        # Filter sensitive headers
        headers = {
            key: value for key, value in request.headers.items()
            if key.lower() not in self.SENSITIVE_HEADERS
        }
        
        # Log request
        logger.info(
            "Incoming request",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=client_ip,
            user_agent=user_agent,
            user_id=user_id,
            headers=headers if settings.DEBUG else {},
        )
        
        # Log request body for POST/PUT/PATCH requests (if not too large)
        if request.method in ["POST", "PUT", "PATCH"]:
            await self._log_request_body(request, request_id)
    
    async def _log_request_body(self, request: Request, request_id: str):
        """Log request body for write operations."""
        try:
            # Check content type
            content_type = request.headers.get("content-type", "")
            
            # Only log JSON and form data
            if not any(ct in content_type.lower() for ct in ["application/json", "application/x-www-form-urlencoded"]):
                return
            
            # Get body
            body = await request.body()
            
            # Skip if body is too large
            if len(body) > 10000:  # 10KB limit
                logger.info(
                    "Request body too large to log",
                    request_id=request_id,
                    body_size=len(body),
                )
                return
            
            # Log body (be careful with sensitive data)
            if settings.DEBUG:
                try:
                    body_str = body.decode("utf-8")
                    logger.debug(
                        "Request body",
                        request_id=request_id,
                        body=body_str,
                    )
                except UnicodeDecodeError:
                    logger.debug(
                        "Request body (binary)",
                        request_id=request_id,
                        body_size=len(body),
                    )
        
        except Exception as e:
            logger.warning(
                "Failed to log request body",
                request_id=request_id,
                error=str(e),
            )
    
    async def _log_response(
        self, 
        request: Request, 
        response: Response, 
        request_id: str, 
        processing_time: float
    ):
        """Log response details."""
        
        # Get user ID if available
        user_id = getattr(request.state, "user_id", None)
        
        # Determine log level based on status code
        if response.status_code >= 500:
            log_level = "error"
        elif response.status_code >= 400:
            log_level = "warning"
        else:
            log_level = "info"
        
        # Log response
        getattr(logger, log_level)(
            "Request completed",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            processing_time=processing_time,
            user_id=user_id,
            response_size=len(response.body) if hasattr(response, 'body') else None,
        )
        
        # Log slow requests
        if processing_time > 1.0:  # 1 second threshold
            logger.warning(
                "Slow request detected",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                processing_time=processing_time,
                user_id=user_id,
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"


class AuditMiddleware(BaseHTTPMiddleware):
    """Audit middleware for tracking data changes and user actions."""
    
    # Operations that should be audited
    AUDIT_METHODS = {"POST", "PUT", "PATCH", "DELETE"}
    
    # Paths that should be audited
    AUDIT_PATHS = {
        "/api/v1/users",
        "/api/v1/inventory",
        "/api/v1/receiving",
        "/api/v1/shipment",
        "/api/v1/production",
    }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and create audit log if needed."""
        
        # Check if this request should be audited
        should_audit = (
            settings.ENABLE_AUDIT_LOGGING and
            request.method in self.AUDIT_METHODS and
            any(request.url.path.startswith(path) for path in self.AUDIT_PATHS)
        )
        
        if not should_audit:
            return await call_next(request)
        
        # Get request details for audit
        request_id = getattr(request.state, "request_id", None)
        user_id = getattr(request.state, "user_id", None)
        
        # Process request
        response = await call_next(request)
        
        # Create audit log entry
        await self._create_audit_log(request, response, request_id, user_id)
        
        return response
    
    async def _create_audit_log(
        self, 
        request: Request, 
        response: Response, 
        request_id: str, 
        user_id: str
    ):
        """Create audit log entry."""
        try:
            audit_data = {
                "request_id": request_id,
                "user_id": user_id,
                "action": request.method,
                "resource": request.url.path,
                "status_code": response.status_code,
                "timestamp": time.time(),
                "client_ip": self._get_client_ip(request),
                "user_agent": request.headers.get("user-agent"),
            }
            
            # Log audit entry
            logger.info(
                "Audit log entry",
                **audit_data,
            )
            
            # TODO: Store audit log in database
            # This would typically involve saving to an audit_logs table
            
        except Exception as e:
            logger.error(
                "Failed to create audit log",
                request_id=request_id,
                error=str(e),
                exc_info=True,
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
