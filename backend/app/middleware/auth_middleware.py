"""
Smart Factory WMS - Authentication Middleware

This middleware handles JWT token validation and user authentication
for protected endpoints.
"""

from typing import Optional
from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import structlog

from app.core.security import verify_token
from app.core.exceptions import AuthenticationException, InvalidTokenException

logger = structlog.get_logger()

# Security scheme for OpenAPI documentation
security = HTTPBearer()


class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for JWT token validation."""
    
    # Public endpoints that don't require authentication
    PUBLIC_ENDPOINTS = {
        "/",
        "/health",
        "/api/v1/auth/login",
        "/api/v1/auth/register",
        "/api/v1/auth/refresh",
        "/api/v1/auth/forgot-password",
        "/api/v1/auth/reset-password",
        "/docs",
        "/redoc",
        "/openapi.json",
    }
    
    async def dispatch(self, request: Request, call_next):
        """Process request and validate authentication if required."""
        
        # Skip authentication for public endpoints
        if self._is_public_endpoint(request.url.path):
            return await call_next(request)
        
        # Skip authentication for OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return await call_next(request)
        
        try:
            # Extract and validate token
            user_id = await self._validate_request_auth(request)
            
            # Add user ID to request state for use in endpoints
            request.state.user_id = user_id
            
            # Log authenticated request
            logger.info(
                "Authenticated request",
                user_id=user_id,
                method=request.method,
                path=request.url.path,
                client_ip=self._get_client_ip(request),
            )
            
        except (AuthenticationException, InvalidTokenException) as e:
            logger.warning(
                "Authentication failed",
                error=e.message,
                method=request.method,
                path=request.url.path,
                client_ip=self._get_client_ip(request),
            )
            return Response(
                content=f'{{"error_code": "{e.error_code}", "message": "{e.message}"}}',
                status_code=e.status_code,
                media_type="application/json",
            )
        except Exception as e:
            logger.error(
                "Unexpected authentication error",
                error=str(e),
                method=request.method,
                path=request.url.path,
                exc_info=True,
            )
            return Response(
                content='{"error_code": "INTERNAL_SERVER_ERROR", "message": "Authentication service error"}',
                status_code=500,
                media_type="application/json",
            )
        
        return await call_next(request)
    
    def _is_public_endpoint(self, path: str) -> bool:
        """Check if endpoint is public and doesn't require authentication."""
        # Exact match
        if path in self.PUBLIC_ENDPOINTS:
            return True
        
        # Pattern matching for documentation endpoints
        if path.startswith("/docs") or path.startswith("/redoc"):
            return True
        
        # Pattern matching for static files
        if path.startswith("/static/"):
            return True
        
        return False
    
    async def _validate_request_auth(self, request: Request) -> str:
        """
        Validate request authentication and return user ID.
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: User ID from validated token
            
        Raises:
            AuthenticationException: If authentication fails
            InvalidTokenException: If token is invalid
        """
        # Get authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise AuthenticationException("Missing authorization header")
        
        # Parse bearer token
        try:
            scheme, token = auth_header.split(" ", 1)
            if scheme.lower() != "bearer":
                raise AuthenticationException("Invalid authorization scheme")
        except ValueError:
            raise AuthenticationException("Invalid authorization header format")
        
        # Validate token
        user_id = verify_token(token, token_type="access")
        if not user_id:
            raise InvalidTokenException("Invalid or expired token")
        
        return user_id
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"


async def get_current_user_id(request: Request) -> str:
    """
    Dependency function to get current user ID from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        str: Current user ID
        
    Raises:
        HTTPException: If user is not authenticated
    """
    user_id = getattr(request.state, "user_id", None)
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail="User not authenticated",
        )
    return user_id


async def get_optional_user_id(request: Request) -> Optional[str]:
    """
    Dependency function to get optional user ID from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Optional[str]: Current user ID if authenticated, None otherwise
    """
    return getattr(request.state, "user_id", None)


class APIKeyMiddleware(BaseHTTPMiddleware):
    """API Key middleware for service-to-service authentication."""
    
    def __init__(self, app, api_keys: set):
        super().__init__(app)
        self.api_keys = api_keys
    
    async def dispatch(self, request: Request, call_next):
        """Process request and validate API key if required."""
        
        # Check if this is a service-to-service endpoint
        if not request.url.path.startswith("/api/internal/"):
            return await call_next(request)
        
        # Get API key from header
        api_key = request.headers.get("X-API-Key")
        if not api_key or api_key not in self.api_keys:
            logger.warning(
                "Invalid API key",
                path=request.url.path,
                client_ip=self._get_client_ip(request),
            )
            return Response(
                content='{"error_code": "INVALID_API_KEY", "message": "Invalid API key"}',
                status_code=401,
                media_type="application/json",
            )
        
        logger.info(
            "API key authenticated",
            path=request.url.path,
            client_ip=self._get_client_ip(request),
        )
        
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
