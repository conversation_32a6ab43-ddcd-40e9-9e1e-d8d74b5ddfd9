"""
Smart Factory WMS - Custom Exceptions

This module contains custom exception classes for the Smart Factory WMS application.
All exceptions include error codes, messages, and optional details for better error handling.
"""

from typing import Any, Dict, Optional


class SmartFactoryException(Exception):
    """Base exception class for Smart Factory WMS."""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


# Authentication & Authorization Exceptions
class AuthenticationException(SmartFactoryException):
    """Exception raised for authentication failures."""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_FAILED",
            status_code=401,
            details=details,
        )


class AuthorizationException(SmartFactoryException):
    """Exception raised for authorization failures."""
    
    def __init__(self, message: str = "Access denied", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="ACCESS_DENIED",
            status_code=403,
            details=details,
        )


class InvalidTokenException(SmartFactoryException):
    """Exception raised for invalid tokens."""
    
    def __init__(self, message: str = "Invalid token", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="INVALID_TOKEN",
            status_code=401,
            details=details,
        )


class TokenExpiredException(SmartFactoryException):
    """Exception raised for expired tokens."""

    def __init__(self, message: str = "Token has expired", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="TOKEN_EXPIRED",
            status_code=401,
            details=details,
        )


class AccountLockedException(SmartFactoryException):
    """Exception raised when user account is locked."""

    def __init__(self, message: str = "Account is locked", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="ACCOUNT_LOCKED",
            status_code=423,
            details=details,
        )


class UserNotFoundException(SmartFactoryException):
    """Exception raised when user is not found."""

    def __init__(self, message: str = "User not found", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="USER_NOT_FOUND",
            status_code=404,
            details=details,
        )


# Validation Exceptions
class ValidationException(SmartFactoryException):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, field: str = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if field:
            error_details["field"] = field
        
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=422,
            details=error_details,
        )


class DuplicateResourceException(SmartFactoryException):
    """Exception raised when trying to create a duplicate resource."""
    
    def __init__(self, resource: str, identifier: str, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        error_details.update({"resource": resource, "identifier": identifier})
        
        super().__init__(
            message=f"{resource} with identifier '{identifier}' already exists",
            error_code="DUPLICATE_RESOURCE",
            status_code=409,
            details=error_details,
        )


# Resource Exceptions
class ResourceNotFoundException(SmartFactoryException):
    """Exception raised when a resource is not found."""
    
    def __init__(self, resource: str, identifier: str, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        error_details.update({"resource": resource, "identifier": identifier})
        
        super().__init__(
            message=f"{resource} with identifier '{identifier}' not found",
            error_code="RESOURCE_NOT_FOUND",
            status_code=404,
            details=error_details,
        )


class ResourceConflictException(SmartFactoryException):
    """Exception raised when there's a conflict with resource state."""
    
    def __init__(self, message: str, resource: str = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if resource:
            error_details["resource"] = resource
        
        super().__init__(
            message=message,
            error_code="RESOURCE_CONFLICT",
            status_code=409,
            details=error_details,
        )


# Business Logic Exceptions
class InsufficientInventoryException(SmartFactoryException):
    """Exception raised when there's insufficient inventory."""
    
    def __init__(
        self, 
        product_id: str, 
        requested: float, 
        available: float, 
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        error_details.update({
            "product_id": product_id,
            "requested_quantity": requested,
            "available_quantity": available,
        })
        
        super().__init__(
            message=f"Insufficient inventory for product {product_id}. Requested: {requested}, Available: {available}",
            error_code="INSUFFICIENT_INVENTORY",
            status_code=409,
            details=error_details,
        )


class InvalidOperationException(SmartFactoryException):
    """Exception raised for invalid business operations."""
    
    def __init__(self, operation: str, reason: str, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        error_details.update({"operation": operation, "reason": reason})
        
        super().__init__(
            message=f"Invalid operation '{operation}': {reason}",
            error_code="INVALID_OPERATION",
            status_code=400,
            details=error_details,
        )


class ProductionException(SmartFactoryException):
    """Exception raised for production-related errors."""
    
    def __init__(self, message: str, production_order_id: str = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if production_order_id:
            error_details["production_order_id"] = production_order_id
        
        super().__init__(
            message=message,
            error_code="PRODUCTION_ERROR",
            status_code=400,
            details=error_details,
        )


class QualityControlException(SmartFactoryException):
    """Exception raised for quality control failures."""
    
    def __init__(self, message: str, inspection_id: str = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if inspection_id:
            error_details["inspection_id"] = inspection_id
        
        super().__init__(
            message=message,
            error_code="QUALITY_CONTROL_FAILURE",
            status_code=400,
            details=error_details,
        )


# External Service Exceptions
class ExternalServiceException(SmartFactoryException):
    """Exception raised for external service failures."""
    
    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        error_details["service"] = service
        
        super().__init__(
            message=f"External service '{service}' error: {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=502,
            details=error_details,
        )


class DatabaseException(SmartFactoryException):
    """Exception raised for database-related errors."""
    
    def __init__(self, message: str, operation: str = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
        
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=500,
            details=error_details,
        )


# Rate Limiting Exception
class RateLimitException(SmartFactoryException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429,
            details=details,
        )


# File Processing Exceptions
class FileProcessingException(SmartFactoryException):
    """Exception raised for file processing errors."""
    
    def __init__(self, message: str, filename: str = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if filename:
            error_details["filename"] = filename
        
        super().__init__(
            message=message,
            error_code="FILE_PROCESSING_ERROR",
            status_code=400,
            details=error_details,
        )


class InvalidFileTypeException(SmartFactoryException):
    """Exception raised for invalid file types."""
    
    def __init__(self, filename: str, allowed_types: list, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        error_details.update({
            "filename": filename,
            "allowed_types": allowed_types,
        })
        
        super().__init__(
            message=f"Invalid file type for '{filename}'. Allowed types: {', '.join(allowed_types)}",
            error_code="INVALID_FILE_TYPE",
            status_code=400,
            details=error_details,
        )
