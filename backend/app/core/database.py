"""
Smart Factory WMS - Database Configuration

This module contains database connection setup, session management,
and database utilities for the Smart Factory WMS application.
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import Null<PERSON>ool
import structlog

from app.core.config import settings

logger = structlog.get_logger()

# Create async engine
engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    poolclass=NullPool if settings.ENVIRONMENT == "test" else None,
    echo=settings.DEBUG,
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# Create declarative base
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function to get database session.
    
    Yields:
        AsyncSession: Database session instance
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error("Database session error", error=str(e), exc_info=True)
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Initialize database tables.
    
    This function creates all tables defined in the models.
    Should be called during application startup.
    """
    try:
        async with engine.begin() as conn:
            # Import all models to ensure they are registered
            from app.models import user  # noqa
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e), exc_info=True)
        raise


async def close_db() -> None:
    """
    Close database connections.
    
    Should be called during application shutdown.
    """
    try:
        await engine.dispose()
        logger.info("Database connections closed successfully")
    except Exception as e:
        logger.error("Failed to close database connections", error=str(e), exc_info=True)
        raise


class DatabaseManager:
    """Database manager for handling database operations."""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = AsyncSessionLocal
    
    async def create_session(self) -> AsyncSession:
        """Create a new database session."""
        return self.session_factory()
    
    async def health_check(self) -> bool:
        """
        Check database health.
        
        Returns:
            bool: True if database is healthy, False otherwise
        """
        try:
            async with self.session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    async def get_connection_info(self) -> dict:
        """
        Get database connection information.
        
        Returns:
            dict: Connection information
        """
        return {
            "url": str(self.engine.url).replace(self.engine.url.password or "", "***"),
            "pool_size": self.engine.pool.size(),
            "checked_in": self.engine.pool.checkedin(),
            "checked_out": self.engine.pool.checkedout(),
            "overflow": self.engine.pool.overflow(),
        }


# Create database manager instance
db_manager = DatabaseManager()


# Database utilities
async def execute_raw_sql(sql: str, params: dict = None) -> list:
    """
    Execute raw SQL query.
    
    Args:
        sql: SQL query string
        params: Query parameters
        
    Returns:
        list: Query results
    """
    async with AsyncSessionLocal() as session:
        result = await session.execute(sql, params or {})
        return result.fetchall()


async def get_table_info(table_name: str) -> dict:
    """
    Get information about a database table.
    
    Args:
        table_name: Name of the table
        
    Returns:
        dict: Table information
    """
    sql = """
    SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
    FROM information_schema.columns 
    WHERE table_name = :table_name
    ORDER BY ordinal_position
    """
    
    async with AsyncSessionLocal() as session:
        result = await session.execute(sql, {"table_name": table_name})
        columns = result.fetchall()
        
        return {
            "table_name": table_name,
            "columns": [
                {
                    "name": col.column_name,
                    "type": col.data_type,
                    "nullable": col.is_nullable == "YES",
                    "default": col.column_default,
                }
                for col in columns
            ],
        }
