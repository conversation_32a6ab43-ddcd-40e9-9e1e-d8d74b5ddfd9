"""
Smart Factory WMS - Authentication API Endpoints

This module contains authentication-related API endpoints including login,
registration, token refresh, and password management.
"""

from datetime import datetime, timedelta
from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import H<PERSON><PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import get_db
from app.core.security import (
    create_access_token, create_refresh_token, verify_token,
    verify_password, get_password_hash, validate_password
)
from app.core.exceptions import (
    AuthenticationException, ValidationException,
    ResourceNotFoundException, DuplicateResourceException,
    AccountLockedException, UserNotFoundException, RateLimitException
)
from app.schemas.auth import (
    LoginRequest, LoginResponse, RegisterRequest, RegisterResponse,
    RefreshTokenRequest, RefreshTokenResponse, ChangePasswordRequest,
    ForgotPasswordRequest, ResetPasswordRequest, UserResponse
)
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.middleware.auth_middleware import get_current_user_id

logger = structlog.get_logger()
router = APIRouter()
security = HTTPBearer()


@router.post("/login", response_model=LoginResponse)
async def login(
    request: LoginRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate user with username/email and password.

    This endpoint supports authentication with either username or email address.
    The system implements rate limiting and account locking for security.

    Args:
        request: Login credentials (username/email and password)
        http_request: HTTP request object for audit logging
        db: Database session

    Returns:
        LoginResponse: Access token, refresh token, and user information

    Raises:
        HTTPException:
            - 401: Invalid credentials
            - 404: User not found
            - 423: Account locked
            - 429: Too many requests (rate limit exceeded)
            - 500: Server error

    Headers:
        X-Request-ID: Optional request ID for tracing
    """
    try:
        auth_service = AuthService(db)
        
        # Get client information for audit logging
        client_ip = http_request.headers.get("X-Forwarded-For", http_request.client.host)
        user_agent = http_request.headers.get("User-Agent", "")
        
        # Authenticate user
        result = await auth_service.authenticate_user(
            username=request.username,
            password=request.password,
            client_ip=client_ip,
            user_agent=user_agent,
            remember_me=request.remember_me
        )
        
        logger.info(
            "User login successful",
            user_id=result["user"].id,
            username=request.username,
            client_ip=client_ip
        )
        
        # Create user response without roles to avoid lazy loading issues
        user_data = {
            "id": result["user"].id,
            "email": result["user"].email,
            "username": result["user"].username,
            "first_name": result["user"].first_name,
            "last_name": result["user"].last_name,
            "phone": result["user"].phone,
            "avatar_url": result["user"].avatar_url,
            "is_active": result["user"].is_active,
            "is_verified": result["user"].is_verified,
            "language": result["user"].language,
            "timezone": result["user"].timezone,
            "last_login_at": result["user"].last_login_at,
            "created_at": result["user"].created_at,
            "roles": []  # Empty for now, can be loaded separately if needed
        }

        return LoginResponse(
            access_token=result["access_token"],
            refresh_token=result["refresh_token"],
            token_type="bearer",
            expires_in=result["expires_in"],
            user=UserResponse(**user_data)
        )
        
    except UserNotFoundException as e:
        logger.warning(
            "User login failed - user not found",
            username=request.username,
            error=e.message,
            client_ip=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except AccountLockedException as e:
        logger.warning(
            "User login failed - account locked",
            username=request.username,
            error=e.message,
            client_ip=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail=e.message
        )
    except RateLimitException as e:
        logger.warning(
            "User login failed - rate limit exceeded",
            username=request.username,
            error=e.message,
            client_ip=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=e.message
        )
    except AuthenticationException as e:
        logger.warning(
            "User login failed",
            username=request.username,
            error=e.message,
            client_ip=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message
        )
    except Exception as e:
        logger.error(
            "Login error",
            username=request.username,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/register", response_model=RegisterResponse)
async def register(
    request: RegisterRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user account.
    
    Args:
        request: Registration data
        http_request: HTTP request object for audit logging
        db: Database session
        
    Returns:
        RegisterResponse: Registration confirmation
        
    Raises:
        HTTPException: If registration fails
    """
    try:
        user_service = UserService(db)
        
        # Validate password
        is_valid, errors = validate_password(request.password)
        if not is_valid:
            raise ValidationException("Password validation failed", details={"errors": errors})
        
        # Check if user already exists
        existing_user = await user_service.get_user_by_email(request.email)
        if existing_user:
            raise DuplicateResourceException("User", request.email)
        
        existing_user = await user_service.get_user_by_username(request.username)
        if existing_user:
            raise DuplicateResourceException("User", request.username)
        
        # Create user
        user_data = {
            "email": request.email,
            "username": request.username,
            "first_name": request.first_name,
            "last_name": request.last_name,
            "phone": request.phone,
            "hashed_password": get_password_hash(request.password),
            "language": request.language or "en",
            "timezone": request.timezone or "UTC"
        }
        
        user = await user_service.create_user(user_data)
        
        logger.info(
            "User registration successful",
            user_id=user.id,
            email=request.email,
            username=request.username
        )
        
        # Create user response without roles to avoid lazy loading issues
        user_data = {
            "id": user.id,
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": user.phone,
            "avatar_url": user.avatar_url,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "language": user.language,
            "timezone": user.timezone,
            "last_login_at": user.last_login_at,
            "created_at": user.created_at,
            "roles": []  # Empty for now
        }

        return RegisterResponse(
            message="User registered successfully",
            user=UserResponse(**user_data)
        )
        
    except (ValidationException, DuplicateResourceException) as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.message
        )
    except Exception as e:
        logger.error(
            "Registration error",
            email=request.email,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    
    Args:
        request: Refresh token request
        db: Database session
        
    Returns:
        RefreshTokenResponse: New access token and optionally new refresh token
        
    Raises:
        HTTPException: If token refresh fails
    """
    try:
        auth_service = AuthService(db)
        
        # Verify refresh token
        user_id = verify_token(request.refresh_token, token_type="refresh")
        if not user_id:
            raise AuthenticationException("Invalid refresh token")
        
        # Get user
        user_service = UserService(db)
        user = await user_service.get_user_by_id(int(user_id))
        if not user or not user.is_active:
            raise AuthenticationException("User not found or inactive")
        
        # Generate new tokens
        result = await auth_service.refresh_user_token(user, request.refresh_token)
        
        logger.info(
            "Token refresh successful",
            user_id=user.id
        )
        
        return RefreshTokenResponse(
            access_token=result["access_token"],
            refresh_token=result.get("refresh_token"),
            token_type="bearer",
            expires_in=result["expires_in"]
        )
        
    except AuthenticationException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message
        )
    except Exception as e:
        logger.error(
            "Token refresh error",
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/logout")
async def logout(
    http_request: Request,
    current_user_id: str = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout user and invalidate session.
    
    Args:
        http_request: HTTP request object
        current_user_id: Current authenticated user ID
        db: Database session
        
    Returns:
        Dict: Logout confirmation
    """
    try:
        auth_service = AuthService(db)
        
        # Get authorization header
        auth_header = http_request.headers.get("Authorization", "")
        token = auth_header.replace("Bearer ", "") if auth_header.startswith("Bearer ") else ""
        
        # Invalidate session
        await auth_service.logout_user(int(current_user_id), token)
        
        logger.info(
            "User logout successful",
            user_id=current_user_id
        )
        
        return {"message": "Logout successful"}
        
    except Exception as e:
        logger.error(
            "Logout error",
            user_id=current_user_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user_id: str = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current authenticated user information.
    
    Args:
        current_user_id: Current authenticated user ID
        db: Database session
        
    Returns:
        UserResponse: Current user information
    """
    try:
        user_service = UserService(db)
        user = await user_service.get_user_by_id(int(current_user_id))
        
        if not user:
            raise ResourceNotFoundException("User", current_user_id)
        
        # Create user response without roles to avoid lazy loading issues
        user_data = {
            "id": user.id,
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": user.phone,
            "avatar_url": user.avatar_url,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "language": user.language,
            "timezone": user.timezone,
            "last_login_at": user.last_login_at,
            "created_at": user.created_at,
            "roles": []  # Empty for now
        }

        return UserResponse(**user_data)
        
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except Exception as e:
        logger.error(
            "Get current user error",
            user_id=current_user_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/change-password")
async def change_password(
    request: ChangePasswordRequest,
    current_user_id: str = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Change user password.
    
    Args:
        request: Password change request
        current_user_id: Current authenticated user ID
        db: Database session
        
    Returns:
        Dict: Password change confirmation
    """
    try:
        user_service = UserService(db)
        
        # Get current user
        user = await user_service.get_user_by_id(int(current_user_id))
        if not user:
            raise ResourceNotFoundException("User", current_user_id)
        
        # Verify current password
        if not verify_password(request.current_password, user.hashed_password):
            raise AuthenticationException("Current password is incorrect")
        
        # Validate new password
        is_valid, errors = validate_password(request.new_password)
        if not is_valid:
            raise ValidationException("Password validation failed", details={"errors": errors})
        
        # Update password
        await user_service.update_user_password(user.id, request.new_password)
        
        logger.info(
            "Password change successful",
            user_id=current_user_id
        )
        
        return {"message": "Password changed successfully"}
        
    except (AuthenticationException, ValidationException, ResourceNotFoundException) as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.message
        )
    except Exception as e:
        logger.error(
            "Change password error",
            user_id=current_user_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/forgot-password")
async def forgot_password(
    request: ForgotPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Initiate password reset process.
    
    Args:
        request: Forgot password request
        db: Database session
        
    Returns:
        Dict: Password reset initiation confirmation
    """
    try:
        auth_service = AuthService(db)
        
        # Initiate password reset (always return success for security)
        await auth_service.initiate_password_reset(request.email)
        
        logger.info(
            "Password reset initiated",
            email=request.email
        )
        
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        logger.error(
            "Forgot password error",
            email=request.email,
            error=str(e),
            exc_info=True
        )
        # Always return success for security
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    request: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Reset user password using reset token.
    
    Args:
        request: Password reset request
        db: Database session
        
    Returns:
        Dict: Password reset confirmation
    """
    try:
        auth_service = AuthService(db)
        
        # Validate new password
        is_valid, errors = validate_password(request.new_password)
        if not is_valid:
            raise ValidationException("Password validation failed", details={"errors": errors})
        
        # Reset password
        await auth_service.reset_password(request.token, request.new_password)
        
        logger.info(
            "Password reset successful",
            token=request.token[:8] + "..."  # Log only first 8 characters for security
        )
        
        return {"message": "Password reset successfully"}
        
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.message
        )
    except AuthenticationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(
            "Reset password error",
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
