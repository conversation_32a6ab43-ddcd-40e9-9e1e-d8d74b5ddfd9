"""
Smart Factory WMS - Authentication Schemas

This module contains Pydantic schemas for authentication-related
requests and responses.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator
import re

from app.core.config import settings


class LoginRequest(BaseModel):
    """Schema for user login request."""

    username: str = Field(..., min_length=3, max_length=50, description="Username or email address")
    password: str = Field(..., min_length=8, max_length=128, description="User password")
    remember_me: bool = Field(default=False, description="Remember user session")

    @validator('username')
    def validate_username(cls, v):
        """Validate username format - allow alphanumeric, dots, underscores, or email format."""
        # Check if it's an email format
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        # Check if it's a username format (alphanumeric, dots, underscores)
        username_pattern = r'^[a-zA-Z0-9._]+$'

        if not (re.match(email_pattern, v) or re.match(username_pattern, v)):
            raise ValueError('Username must be alphanumeric with dots/underscores allowed, or a valid email address')
        return v

    @validator('password')
    def validate_password_length(cls, v):
        """Validate password length according to requirements."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if len(v) > 128:
            raise ValueError('Password must not exceed 128 characters')
        return v

    class Config:
        schema_extra = {
            "example": {
                "username": "<EMAIL>",
                "password": "password123",
                "remember_me": False
            }
        }


class RegisterRequest(BaseModel):
    """Schema for user registration request."""
    
    email: EmailStr = Field(..., description="User email address")
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH, description="User password")
    first_name: str = Field(..., min_length=1, max_length=50, description="First name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Last name")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    language: Optional[str] = Field("en", max_length=5, description="Preferred language")
    timezone: Optional[str] = Field("UTC", max_length=50, description="User timezone")
    
    @validator('username')
    def validate_username(cls, v):
        """Validate username format."""
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Username can only contain letters, numbers, underscores, and hyphens')
        return v
    
    @validator('phone')
    def validate_phone(cls, v):
        """Validate phone number format."""
        if v and not re.match(r'^\+?[1-9]\d{1,14}$', v):
            raise ValueError('Invalid phone number format')
        return v
    
    @validator('language')
    def validate_language(cls, v):
        """Validate language code."""
        if v and v not in settings.SUPPORTED_LANGUAGES:
            raise ValueError(f'Language must be one of: {", ".join(settings.SUPPORTED_LANGUAGES)}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "username": "newuser",
                "password": "SecurePass123!",
                "first_name": "John",
                "last_name": "Doe",
                "phone": "+1234567890",
                "language": "en",
                "timezone": "UTC"
            }
        }


class RefreshTokenRequest(BaseModel):
    """Schema for token refresh request."""
    
    refresh_token: str = Field(..., description="Refresh token")
    
    class Config:
        schema_extra = {
            "example": {
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            }
        }


class ChangePasswordRequest(BaseModel):
    """Schema for password change request."""
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH, description="New password")
    
    class Config:
        schema_extra = {
            "example": {
                "current_password": "oldpassword123",
                "new_password": "NewSecurePass123!"
            }
        }


class ForgotPasswordRequest(BaseModel):
    """Schema for forgot password request."""
    
    email: EmailStr = Field(..., description="User email address")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class ResetPasswordRequest(BaseModel):
    """Schema for password reset request."""
    
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH, description="New password")
    
    class Config:
        schema_extra = {
            "example": {
                "token": "reset_token_here",
                "new_password": "NewSecurePass123!"
            }
        }


class RoleResponse(BaseModel):
    """Schema for role response."""
    
    id: int
    name: str
    display_name: str
    description: Optional[str] = None
    is_active: bool
    
    class Config:
        from_attributes = True


class PermissionResponse(BaseModel):
    """Schema for permission response."""
    
    id: int
    name: str
    display_name: str
    description: Optional[str] = None
    module: str
    action: str
    is_active: bool
    
    class Config:
        from_attributes = True


class UserResponse(BaseModel):
    """Schema for user response."""
    
    id: int
    email: str
    username: str
    first_name: str
    last_name: str
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool
    is_verified: bool
    language: str
    timezone: str
    last_login_at: Optional[datetime] = None
    created_at: datetime
    roles: List[RoleResponse] = []
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
    
    class Config:
        from_attributes = True


class LoginResponse(BaseModel):
    """Schema for login response."""
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserResponse = Field(..., description="User information")
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "expires_in": 1800,
                "user": {
                    "id": 1,
                    "email": "<EMAIL>",
                    "username": "user",
                    "first_name": "John",
                    "last_name": "Doe",
                    "is_active": True,
                    "is_verified": True,
                    "language": "en",
                    "timezone": "UTC",
                    "roles": []
                }
            }
        }


class RegisterResponse(BaseModel):
    """Schema for registration response."""
    
    message: str = Field(..., description="Registration confirmation message")
    user: UserResponse = Field(..., description="Created user information")
    
    class Config:
        schema_extra = {
            "example": {
                "message": "User registered successfully",
                "user": {
                    "id": 1,
                    "email": "<EMAIL>",
                    "username": "newuser",
                    "first_name": "John",
                    "last_name": "Doe",
                    "is_active": True,
                    "is_verified": False,
                    "language": "en",
                    "timezone": "UTC",
                    "roles": []
                }
            }
        }


class RefreshTokenResponse(BaseModel):
    """Schema for token refresh response."""
    
    access_token: str = Field(..., description="New JWT access token")
    refresh_token: Optional[str] = Field(None, description="New JWT refresh token (if rotated)")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "expires_in": 1800
            }
        }


class TokenValidationResponse(BaseModel):
    """Schema for token validation response."""
    
    valid: bool = Field(..., description="Whether token is valid")
    user_id: Optional[int] = Field(None, description="User ID if token is valid")
    expires_at: Optional[datetime] = Field(None, description="Token expiration time")
    
    class Config:
        schema_extra = {
            "example": {
                "valid": True,
                "user_id": 1,
                "expires_at": "2023-12-31T23:59:59Z"
            }
        }
