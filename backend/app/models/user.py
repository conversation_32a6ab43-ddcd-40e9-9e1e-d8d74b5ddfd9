"""
Smart Factory WMS - User Models

This module contains user-related database models including User, Role, and Permission.
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from app.core.database import Base


# Association tables for many-to-many relationships
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('users.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('role_id', Integer, Foreign<PERSON>ey('roles.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('created_at', DateTime(timezone=True), server_default=func.now()),
    <PERSON>umn('created_by', Integer, Foreign<PERSON>ey('users.id')),
)

role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', Inte<PERSON>, <PERSON><PERSON><PERSON>('roles.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('permission_id', Integer, <PERSON><PERSON><PERSON>('permissions.id', ondelete='CASCADE'), primary_key=True),
    Column('created_at', DateTime(timezone=True), server_default=func.now()),
    Column('created_by', Integer, ForeignKey('users.id')),
)


class User(Base):
    """User model for authentication and authorization."""
    
    __tablename__ = 'users'
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Basic information
    email: Mapped[str] = mapped_column(String(254), unique=True, index=True, nullable=False)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # Personal information
    first_name: Mapped[str] = mapped_column(String(50), nullable=False)
    last_name: Mapped[str] = mapped_column(String(50), nullable=False)
    phone: Mapped[Optional[str]] = mapped_column(String(20))
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Status and settings
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Preferences
    language: Mapped[str] = mapped_column(String(5), default='en', nullable=False)
    timezone: Mapped[str] = mapped_column(String(50), default='UTC', nullable=False)
    
    # Security
    failed_login_attempts: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    locked_until: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    password_changed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    last_login_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    last_login_ip: Mapped[Optional[str]] = mapped_column(String(45))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))
    updated_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))
    
    # Relationships
    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary=user_roles,
        back_populates="users",
        primaryjoin="User.id == user_roles.c.user_id",
        secondaryjoin="Role.id == user_roles.c.role_id"
    )
    created_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[created_by], remote_side=[id])
    updated_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[updated_by], remote_side=[id])
    
    # Audit trail relationships
    user_sessions: Mapped[List["UserSession"]] = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    audit_logs: Mapped[List["UserAuditLog"]] = relationship("UserAuditLog", back_populates="user", cascade="all, delete-orphan")
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"


class Role(Base):
    """Role model for role-based access control."""
    
    __tablename__ = 'roles'
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Basic information
    name: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    display_name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_system: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)  # System roles cannot be deleted
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))
    updated_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))
    
    # Relationships
    users: Mapped[List["User"]] = relationship(
        "User",
        secondary=user_roles,
        back_populates="roles",
        primaryjoin="Role.id == user_roles.c.role_id",
        secondaryjoin="User.id == user_roles.c.user_id"
    )
    permissions: Mapped[List["Permission"]] = relationship("Permission", secondary=role_permissions, back_populates="roles")
    created_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[created_by])
    updated_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self) -> str:
        return f"<Role(id={self.id}, name='{self.name}')>"


class Permission(Base):
    """Permission model for fine-grained access control."""
    
    __tablename__ = 'permissions'
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Basic information
    name: Mapped[str] = mapped_column(String(100), unique=True, index=True, nullable=False)
    display_name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Categorization
    module: Mapped[str] = mapped_column(String(50), nullable=False, index=True)  # e.g., 'inventory', 'receiving'
    action: Mapped[str] = mapped_column(String(50), nullable=False, index=True)  # e.g., 'view', 'create', 'update', 'delete'
    
    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_system: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)  # System permissions cannot be deleted
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))
    updated_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))
    
    # Relationships
    roles: Mapped[List["Role"]] = relationship("Role", secondary=role_permissions, back_populates="permissions")
    created_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[created_by])
    updated_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self) -> str:
        return f"<Permission(id={self.id}, name='{self.name}', module='{self.module}', action='{self.action}')>"


class UserSession(Base):
    """User session model for tracking active sessions."""
    
    __tablename__ = 'user_sessions'
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Session information
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    session_token: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    refresh_token: Mapped[Optional[str]] = mapped_column(String(255), unique=True, index=True)
    
    # Device and location information
    device_type: Mapped[Optional[str]] = mapped_column(String(50))  # 'web', 'mobile', 'api'
    device_name: Mapped[Optional[str]] = mapped_column(String(100))
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    location: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Session status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_activity_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    ended_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="user_sessions")
    
    def __repr__(self) -> str:
        return f"<UserSession(id={self.id}, user_id={self.user_id}, device_type='{self.device_type}')>"


class UserAuditLog(Base):
    """User audit log model for tracking user actions."""
    
    __tablename__ = 'user_audit_logs'
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # User and action information
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    action: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    resource: Mapped[Optional[str]] = mapped_column(String(100), index=True)
    resource_id: Mapped[Optional[str]] = mapped_column(String(100), index=True)
    
    # Request information
    method: Mapped[Optional[str]] = mapped_column(String(10))  # HTTP method
    endpoint: Mapped[Optional[str]] = mapped_column(String(200))
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    
    # Additional data
    old_values: Mapped[Optional[str]] = mapped_column(Text)  # JSON string of old values
    new_values: Mapped[Optional[str]] = mapped_column(Text)  # JSON string of new values
    extra_metadata: Mapped[Optional[str]] = mapped_column(Text)    # JSON string of additional metadata
    
    # Status
    status: Mapped[str] = mapped_column(String(20), default='success', nullable=False)  # 'success', 'failed', 'error'
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamp
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="audit_logs")
    
    def __repr__(self) -> str:
        return f"<UserAuditLog(id={self.id}, user_id={self.user_id}, action='{self.action}')>"


# For backward compatibility, create alias classes
UserRole = user_roles
RolePermission = role_permissions
