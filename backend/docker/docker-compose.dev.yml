# Smart Factory WMS - Development Docker Compose Override
# This file extends the base docker-compose.yml for development

version: '3.8'

services:
  # Backend with development overrides
  backend:
    build:
      context: ..
      dockerfile: Dockerfile.dev
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - RELOAD=true
    volumes:
      - ../:/app
      - /app/__pycache__
      - /app/.pytest_cache
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    
  # Development database with exposed port
  postgres:
    environment:
      POSTGRES_DB: smart_factory_dev
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    
  # Development Redis
  redis:
    ports:
      - "6380:6379"  # Different port to avoid conflicts
