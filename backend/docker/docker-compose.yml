# Smart Factory WMS - Docker Compose Configuration
# This file defines the complete application stack for development and testing

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: smart_factory_postgres
    environment:
      POSTGRES_DB: smart_factory
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - smart_factory_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: smart_factory_redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - smart_factory_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: smart_factory_backend
    environment:
      - DATABASE_URL=********************************************/smart_factory
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - DEBUG=true
      - ENVIRONMENT=development
      - CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://localhost:5173"]
    volumes:
      - ../app:/app/app
      - backend_logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - smart_factory_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # PgAdmin (Database Administration)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: smart_factory_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    networks:
      - smart_factory_network
    depends_on:
      - postgres
    restart: unless-stopped

  # Redis Commander (Redis Administration)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: smart_factory_redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379:0:redis_password
    ports:
      - "8081:8081"
    networks:
      - smart_factory_network
    depends_on:
      - redis
    restart: unless-stopped

  # Nginx (Reverse Proxy)
  nginx:
    image: nginx:alpine
    container_name: smart_factory_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - smart_factory_network
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  smart_factory_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
