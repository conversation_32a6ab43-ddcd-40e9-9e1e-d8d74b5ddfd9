"""
Smart Factory WMS - Test Configuration

This module contains pytest configuration and fixtures for testing.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import Async<PERSON>enerator, Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.core.database import Base, get_db
from app.core.config import settings
from app.models.user import User, Role, Permission
from app.services.user_service import UserService
from app.core.security import get_password_hash

# Test database URL (in-memory SQLite for testing)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False,
)

# Create test session factory
TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with TestSessionLocal() as session:
        yield session
    
    # Drop tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """Override the get_db dependency."""
    async def _override_get_db():
        yield db_session
    
    return _override_get_db


@pytest.fixture
def client(override_get_db) -> TestClient:
    """Create a test client."""
    app.dependency_overrides[get_db] = override_get_db
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def async_client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    from httpx import ASGITransport
    app.dependency_overrides[get_db] = override_get_db

    # Use httpx AsyncClient with ASGI transport
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """Create a test user."""
    user_service = UserService(db_session)
    
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "first_name": "Test",
        "last_name": "User",
        "hashed_password": get_password_hash("testpassword"),
        "is_active": True,
        "is_verified": True,
    }
    
    user = await user_service.create_user(user_data)
    return user


@pytest.fixture(autouse=True)
def reset_rate_limits():
    """Reset rate limits before each test."""
    from app.services.rate_limit_service import rate_limit_service
    rate_limit_service.reset_all_rate_limits()
    yield
    rate_limit_service.reset_all_rate_limits()


@pytest_asyncio.fixture
async def test_admin_user(db_session: AsyncSession) -> User:
    """Create a test admin user."""
    user_service = UserService(db_session)
    
    # Create admin role
    admin_role = Role(
        name="admin",
        display_name="Administrator",
        description="System administrator",
        is_active=True,
        is_system=True,
    )
    db_session.add(admin_role)
    await db_session.commit()
    await db_session.refresh(admin_role)
    
    # Create admin user
    user_data = {
        "email": "<EMAIL>",
        "username": "admin",
        "first_name": "Admin",
        "last_name": "User",
        "hashed_password": get_password_hash("adminpassword"),
        "is_active": True,
        "is_verified": True,
        "is_superuser": True,
    }
    
    user = await user_service.create_user(user_data)
    
    # Assign admin role
    await user_service.assign_role_to_user(user.id, admin_role.id)
    
    return user


@pytest_asyncio.fixture
async def test_permissions(db_session: AsyncSession) -> list[Permission]:
    """Create test permissions."""
    permissions = [
        Permission(
            name="users.view",
            display_name="View Users",
            description="View user information",
            module="users",
            action="view",
            is_active=True,
            is_system=True,
        ),
        Permission(
            name="users.create",
            display_name="Create Users",
            description="Create new users",
            module="users",
            action="create",
            is_active=True,
            is_system=True,
        ),
        Permission(
            name="inventory.view",
            display_name="View Inventory",
            description="View inventory information",
            module="inventory",
            action="view",
            is_active=True,
            is_system=True,
        ),
    ]
    
    for permission in permissions:
        db_session.add(permission)
    
    await db_session.commit()
    
    for permission in permissions:
        await db_session.refresh(permission)
    
    return permissions


@pytest_asyncio.fixture
async def test_roles(db_session: AsyncSession, test_permissions: list[Permission]) -> list[Role]:
    """Create test roles with permissions."""
    roles = [
        Role(
            name="user",
            display_name="User",
            description="Regular user",
            is_active=True,
            is_system=True,
        ),
        Role(
            name="manager",
            display_name="Manager",
            description="Manager role",
            is_active=True,
            is_system=True,
        ),
    ]
    
    for role in roles:
        db_session.add(role)
    
    await db_session.commit()
    
    for role in roles:
        await db_session.refresh(role)
    
    # Assign permissions to roles
    user_role = roles[0]
    manager_role = roles[1]
    
    # User role gets view permissions
    user_role.permissions.append(test_permissions[0])  # users.view
    user_role.permissions.append(test_permissions[2])  # inventory.view
    
    # Manager role gets all permissions
    for permission in test_permissions:
        manager_role.permissions.append(permission)
    
    await db_session.commit()
    
    return roles


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """Create authentication headers for test user."""
    from app.core.security import create_access_token
    
    access_token = create_access_token(subject=str(test_user.id))
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_auth_headers(test_admin_user: User) -> dict:
    """Create authentication headers for admin user."""
    from app.core.security import create_access_token
    
    access_token = create_access_token(subject=str(test_admin_user.id))
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    original_debug = settings.DEBUG
    original_testing = getattr(settings, 'TESTING', False)
    
    settings.DEBUG = True
    settings.TESTING = True
    
    yield settings
    
    settings.DEBUG = original_debug
    settings.TESTING = original_testing


# Test data factories
class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def build(**kwargs):
        """Build user data."""
        default_data = {
            "email": "<EMAIL>",
            "username": "user",
            "first_name": "Test",
            "last_name": "User",
            "password": "testpassword",
            "is_active": True,
        }
        default_data.update(kwargs)
        return default_data


class RoleFactory:
    """Factory for creating test roles."""
    
    @staticmethod
    def build(**kwargs):
        """Build role data."""
        default_data = {
            "name": "test_role",
            "display_name": "Test Role",
            "description": "Test role description",
            "is_active": True,
        }
        default_data.update(kwargs)
        return default_data


class PermissionFactory:
    """Factory for creating test permissions."""
    
    @staticmethod
    def build(**kwargs):
        """Build permission data."""
        default_data = {
            "name": "test.permission",
            "display_name": "Test Permission",
            "description": "Test permission description",
            "module": "test",
            "action": "view",
            "is_active": True,
        }
        default_data.update(kwargs)
        return default_data


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
