"""
Smart Factory WMS - Authentication Tests

This module contains tests for authentication functionality.
"""

import pytest
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.core.security import verify_password, get_password_hash


class TestAuthService:
    """Test cases for AuthService."""
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, db_session: AsyncSession, test_user: User):
        """Test successful user authentication."""
        auth_service = AuthService(db_session)
        
        result = await auth_service.authenticate_user(
            username=test_user.email,
            password="testpassword",
            client_ip="127.0.0.1",
            user_agent="test-agent"
        )
        
        assert result["user"].id == test_user.id
        assert "access_token" in result
        assert "refresh_token" in result
        assert result["token_type"] == "bearer"
        assert result["expires_in"] > 0
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_email(self, db_session: AsyncSession):
        """Test authentication with invalid email."""
        auth_service = AuthService(db_session)
        
        with pytest.raises(Exception):  # Should raise UserNotFoundException
            await auth_service.authenticate_user(
                username="<EMAIL>",
                password="testpassword"
            )
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_password(self, db_session: AsyncSession, test_user: User):
        """Test authentication with invalid password."""
        auth_service = AuthService(db_session)
        
        with pytest.raises(Exception):  # Should raise AuthenticationException
            await auth_service.authenticate_user(
                username=test_user.email,
                password="wrongpassword"
            )
    
    @pytest.mark.asyncio
    async def test_authenticate_inactive_user(self, db_session: AsyncSession):
        """Test authentication with inactive user."""
        user_service = UserService(db_session)
        
        # Create inactive user
        user_data = {
            "email": "<EMAIL>",
            "username": "inactive",
            "first_name": "Inactive",
            "last_name": "User",
            "hashed_password": get_password_hash("testpassword"),
            "is_active": False,
        }
        
        user = await user_service.create_user(user_data)
        
        auth_service = AuthService(db_session)
        
        with pytest.raises(Exception):  # Should raise AuthenticationException
            await auth_service.authenticate_user(
                username=user.email,
                password="testpassword"
            )


class TestAuthAPI:
    """Test cases for authentication API endpoints."""
    
    @pytest.mark.asyncio
    async def test_login_success(self, async_client: AsyncClient, test_user: User):
        """Test successful login."""
        response = await async_client.post(
            "/api/v1/auth/login",
            json={
                "username": test_user.email,
                "password": "testpassword",
                "remember_me": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["expires_in"] > 0
        assert data["user"]["id"] == test_user.id
        assert data["user"]["email"] == test_user.email
    
    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, async_client: AsyncClient):
        """Test login with invalid credentials."""
        response = await async_client.post(
            "/api/v1/auth/login",
            json={
                "username": "<EMAIL>",
                "password": "wrongpassword"
            }
        )

        assert response.status_code == 404  # User not found
    
    @pytest.mark.asyncio
    async def test_login_validation_error(self, async_client: AsyncClient):
        """Test login with validation errors."""
        response = await async_client.post(
            "/api/v1/auth/login",
            json={
                "username": "ab",  # Too short
                "password": "short"  # Too short
            }
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_register_success(self, async_client: AsyncClient):
        """Test successful user registration."""
        response = await async_client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "newuser",
                "password": "SecurePass123!",
                "first_name": "New",
                "last_name": "User",
                "phone": "+1234567890"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "User registered successfully"
        assert data["user"]["email"] == "<EMAIL>"
        assert data["user"]["username"] == "newuser"
        assert data["user"]["first_name"] == "New"
        assert data["user"]["last_name"] == "User"
    
    @pytest.mark.asyncio
    async def test_register_duplicate_email(self, async_client: AsyncClient, test_user: User):
        """Test registration with duplicate email."""
        response = await async_client.post(
            "/api/v1/auth/register",
            json={
                "email": test_user.email,
                "username": "newuser",
                "password": "SecurePass123!",
                "first_name": "New",
                "last_name": "User"
            }
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_register_duplicate_username(self, async_client: AsyncClient, test_user: User):
        """Test registration with duplicate username."""
        response = await async_client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": test_user.username,
                "password": "SecurePass123!",
                "first_name": "New",
                "last_name": "User"
            }
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_get_current_user(self, async_client: AsyncClient, test_user: User, auth_headers: dict):
        """Test getting current user information."""
        response = await async_client.get(
            "/api/v1/auth/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == test_user.id
        assert data["email"] == test_user.email
        assert data["username"] == test_user.username
        assert data["first_name"] == test_user.first_name
        assert data["last_name"] == test_user.last_name
    
    @pytest.mark.asyncio
    async def test_get_current_user_unauthorized(self, async_client: AsyncClient):
        """Test getting current user without authentication."""
        response = await async_client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_logout(self, async_client: AsyncClient, auth_headers: dict):
        """Test user logout."""
        response = await async_client.post(
            "/api/v1/auth/logout",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Logout successful"
    
    @pytest.mark.asyncio
    async def test_change_password_success(self, async_client: AsyncClient, test_user: User, auth_headers: dict):
        """Test successful password change."""
        response = await async_client.post(
            "/api/v1/auth/change-password",
            headers=auth_headers,
            json={
                "current_password": "testpassword",
                "new_password": "NewSecurePass123!"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Password changed successfully"
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, async_client: AsyncClient, auth_headers: dict):
        """Test password change with wrong current password."""
        response = await async_client.post(
            "/api/v1/auth/change-password",
            headers=auth_headers,
            json={
                "current_password": "wrongpassword",
                "new_password": "NewSecurePass123!"
            }
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_forgot_password(self, async_client: AsyncClient, test_user: User):
        """Test forgot password functionality."""
        response = await async_client.post(
            "/api/v1/auth/forgot-password",
            json={"email": test_user.email}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "password reset link" in data["message"].lower()
    
    @pytest.mark.asyncio
    async def test_forgot_password_nonexistent_email(self, async_client: AsyncClient):
        """Test forgot password with nonexistent email."""
        response = await async_client.post(
            "/api/v1/auth/forgot-password",
            json={"email": "<EMAIL>"}
        )
        
        # Should still return success for security
        assert response.status_code == 200
        data = response.json()
        assert "password reset link" in data["message"].lower()

    @pytest.mark.asyncio
    async def test_login_with_username(self, async_client: AsyncClient, test_user: User):
        """Test login with username instead of email."""
        response = await async_client.post(
            "/api/v1/auth/login",
            json={
                "username": test_user.username,
                "password": "testpassword",
                "remember_me": False
            }
        )

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["expires_in"] > 0
        assert data["user"]["id"] == test_user.id
        assert data["user"]["username"] == test_user.username

    @pytest.mark.asyncio
    async def test_login_rate_limiting(self, async_client: AsyncClient):
        """Test rate limiting for login attempts."""
        # Make multiple failed login attempts
        for i in range(6):  # Exceed the limit of 5
            response = await async_client.post(
                "/api/v1/auth/login",
                json={
                    "username": "<EMAIL>",
                    "password": "wrongpassword"
                }
            )

            if i < 5:
                assert response.status_code == 404  # User not found
            else:
                assert response.status_code == 429  # Rate limit exceeded

    @pytest.mark.asyncio
    async def test_account_locking(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test account locking after failed attempts."""
        from app.services.user_service import UserService
        from app.core.security import get_password_hash

        user_service = UserService(db_session)

        # Create a test user
        user_data = {
            "email": "<EMAIL>",
            "username": "locktest",
            "first_name": "Lock",
            "last_name": "Test",
            "hashed_password": get_password_hash("testpassword"),
        }

        user = await user_service.create_user(user_data)

        # Make multiple failed login attempts to trigger account lock or rate limiting
        for i in range(6):
            response = await async_client.post(
                "/api/v1/auth/login",
                json={
                    "username": user.username,
                    "password": "wrongpassword"
                }
            )

            if i < 5:
                assert response.status_code == 401  # Invalid credentials
            else:
                # After 5 failed attempts, either account should be locked or rate limited
                assert response.status_code in [423, 429]  # Account locked or rate limited

    @pytest.mark.asyncio
    async def test_request_id_header(self, async_client: AsyncClient, test_user: User):
        """Test X-Request-ID header handling."""
        request_id = "test-request-123"

        response = await async_client.post(
            "/api/v1/auth/login",
            json={
                "username": test_user.email,
                "password": "testpassword",
                "remember_me": False
            },
            headers={"X-Request-ID": request_id}
        )

        assert response.status_code == 200
        assert response.headers.get("X-Request-ID") == request_id


class TestPasswordSecurity:
    """Test cases for password security functions."""
    
    def test_password_hashing(self):
        """Test password hashing and verification."""
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert verify_password(password, hashed)
        assert not verify_password("wrongpassword", hashed)
    
    def test_password_hash_uniqueness(self):
        """Test that password hashes are unique."""
        password = "testpassword123"
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Hashes should be different due to salt
        assert hash1 != hash2
        
        # But both should verify correctly
        assert verify_password(password, hash1)
        assert verify_password(password, hash2)
