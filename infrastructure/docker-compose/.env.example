# Smart Factory WMS - Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-super-secret-key-change-this-in-production
LOG_LEVEL=INFO

# Database Configuration
POSTGRES_DB=smart_factory
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_PORT=5432

# Redis Configuration
REDIS_PASSWORD=your-redis-password
REDIS_PORT=6379

# Backend Configuration
BACKEND_PORT=8000
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Frontend Configuration
FRONTEND_PORT=3000
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_NAME=Smart Factory WMS
VITE_APP_VERSION=1.0.0

# Nginx Configuration
HTTP_PORT=80
HTTPS_PORT=443

# Admin Tools (optional)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=your-pgadmin-password
PGADMIN_PORT=5050
REDIS_COMMANDER_PORT=8081

# Monitoring (optional)
PROMETHEUS_PORT=9090
GRAFANA_USER=admin
GRAFANA_PASSWORD=your-grafana-password
GRAFANA_PORT=3001

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Smart Factory WMS

# AWS Configuration (for production deployment)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=smart-factory-uploads

# Security Settings
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_MIN_LENGTH=8

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_PASSWORD_RESET=true
ENABLE_AUDIT_LOGGING=true

# Business Settings
DEFAULT_TIMEZONE=UTC
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,ja,zh,vi
DEFAULT_CURRENCY=USD

# File Upload Settings
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60
