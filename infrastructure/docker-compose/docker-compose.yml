# Smart Factory WMS - Complete Application Stack
# This file defines the entire application infrastructure

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: smart_factory_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-smart_factory}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - smart_factory_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: smart_factory_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - smart_factory_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ../../backend
      dockerfile: Dockerfile
    container_name: smart_factory_backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-smart_factory}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://localhost:8080}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    networks:
      - smart_factory_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Frontend Web Application
  frontend-web:
    build:
      context: ../../frontend-web
      dockerfile: Dockerfile
    container_name: smart_factory_frontend_web
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000/api/v1}
      - VITE_APP_NAME=${VITE_APP_NAME:-Smart Factory WMS}
      - VITE_APP_VERSION=${VITE_APP_VERSION:-1.0.0}
    volumes:
      - frontend_logs:/var/log/nginx
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    networks:
      - smart_factory_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Nginx Load Balancer / Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: smart_factory_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
      - ssl_certs:/etc/nginx/ssl:ro
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    networks:
      - smart_factory_network
    depends_on:
      - backend
      - frontend-web
    restart: unless-stopped

  # PgAdmin (Database Administration)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: smart_factory_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    networks:
      - smart_factory_network
    depends_on:
      - postgres
    restart: unless-stopped
    profiles:
      - admin

  # Redis Commander (Redis Administration)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: smart_factory_redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD:-redis_password}
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    networks:
      - smart_factory_network
    depends_on:
      - redis
    restart: unless-stopped
    profiles:
      - admin

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: smart_factory_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    networks:
      - smart_factory_network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana (Metrics Visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: smart_factory_grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    networks:
      - smart_factory_network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  frontend_logs:
    driver: local
  nginx_logs:
    driver: local
  ssl_certs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  smart_factory_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
