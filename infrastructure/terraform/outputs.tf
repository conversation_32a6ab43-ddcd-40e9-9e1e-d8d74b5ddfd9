# Smart Factory WMS - Terraform Outputs
# This file defines output values from the infrastructure

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

# Security Group Outputs
output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = aws_security_group.alb.id
}

output "ecs_security_group_id" {
  description = "ID of the ECS security group"
  value       = aws_security_group.ecs.id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = aws_security_group.rds.id
}

output "redis_security_group_id" {
  description = "ID of the Redis security group"
  value       = aws_security_group.redis.id
}

# Load Balancer Outputs
output "alb_dns_name" {
  description = "DNS name of the Application Load Balancer"
  value       = try(aws_lb.main.dns_name, "")
}

output "alb_zone_id" {
  description = "Zone ID of the Application Load Balancer"
  value       = try(aws_lb.main.zone_id, "")
}

output "alb_arn" {
  description = "ARN of the Application Load Balancer"
  value       = try(aws_lb.main.arn, "")
}

# ECS Outputs
output "ecs_cluster_id" {
  description = "ID of the ECS cluster"
  value       = try(aws_ecs_cluster.main.id, "")
}

output "ecs_cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = try(aws_ecs_cluster.main.arn, "")
}

output "backend_service_name" {
  description = "Name of the backend ECS service"
  value       = try(aws_ecs_service.backend.name, "")
}

output "frontend_service_name" {
  description = "Name of the frontend ECS service"
  value       = try(aws_ecs_service.frontend.name, "")
}

# Database Outputs
output "rds_endpoint" {
  description = "RDS instance endpoint"
  value       = try(aws_db_instance.main.endpoint, "")
  sensitive   = true
}

output "rds_port" {
  description = "RDS instance port"
  value       = try(aws_db_instance.main.port, "")
}

output "rds_database_name" {
  description = "RDS database name"
  value       = try(aws_db_instance.main.db_name, "")
}

# Redis Outputs
output "redis_endpoint" {
  description = "Redis cluster endpoint"
  value       = try(aws_elasticache_cluster.main.cache_nodes[0].address, "")
  sensitive   = true
}

output "redis_port" {
  description = "Redis cluster port"
  value       = try(aws_elasticache_cluster.main.cache_nodes[0].port, "")
}

# S3 Outputs
output "s3_bucket_name" {
  description = "Name of the S3 bucket"
  value       = try(aws_s3_bucket.main.bucket, "")
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = try(aws_s3_bucket.main.arn, "")
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = try(aws_s3_bucket.main.bucket_domain_name, "")
}

# CloudFront Outputs
output "cloudfront_distribution_id" {
  description = "ID of the CloudFront distribution"
  value       = try(aws_cloudfront_distribution.main.id, "")
}

output "cloudfront_domain_name" {
  description = "Domain name of the CloudFront distribution"
  value       = try(aws_cloudfront_distribution.main.domain_name, "")
}

# IAM Outputs
output "ecs_task_execution_role_arn" {
  description = "ARN of the ECS task execution role"
  value       = try(aws_iam_role.ecs_task_execution.arn, "")
}

output "ecs_task_role_arn" {
  description = "ARN of the ECS task role"
  value       = try(aws_iam_role.ecs_task.arn, "")
}

# CloudWatch Outputs
output "log_group_backend" {
  description = "Name of the backend log group"
  value       = try(aws_cloudwatch_log_group.backend.name, "")
}

output "log_group_frontend" {
  description = "Name of the frontend log group"
  value       = try(aws_cloudwatch_log_group.frontend.name, "")
}

# Secrets Manager Outputs
output "database_secret_arn" {
  description = "ARN of the database secret"
  value       = try(aws_secretsmanager_secret.database.arn, "")
  sensitive   = true
}

output "app_secrets_arn" {
  description = "ARN of the application secrets"
  value       = try(aws_secretsmanager_secret.app_secrets.arn, "")
  sensitive   = true
}

# Application URLs
output "application_url" {
  description = "URL to access the application"
  value       = var.ssl_certificate_arn != "" ? "https://${try(aws_lb.main.dns_name, "")}" : "http://${try(aws_lb.main.dns_name, "")}"
}

output "api_url" {
  description = "URL to access the API"
  value       = "${var.ssl_certificate_arn != "" ? "https" : "http"}://${try(aws_lb.main.dns_name, "")}/api/v1"
}

# Environment Information
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "aws_region" {
  description = "AWS region"
  value       = var.aws_region
}

# Resource Summary
output "resource_summary" {
  description = "Summary of created resources"
  value = {
    vpc_id                    = aws_vpc.main.id
    public_subnets           = length(aws_subnet.public)
    private_subnets          = length(aws_subnet.private)
    ecs_cluster              = try(aws_ecs_cluster.main.name, "")
    database_engine          = try(aws_db_instance.main.engine, "")
    database_engine_version  = try(aws_db_instance.main.engine_version, "")
    redis_engine_version     = try(aws_elasticache_cluster.main.engine_version, "")
    s3_bucket_created        = try(aws_s3_bucket.main.bucket, "") != ""
    cloudfront_enabled       = try(aws_cloudfront_distribution.main.id, "") != ""
    ssl_enabled              = var.ssl_certificate_arn != ""
  }
}
